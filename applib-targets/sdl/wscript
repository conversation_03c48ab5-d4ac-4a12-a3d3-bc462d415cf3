import waflib


def configure(conf):
    conf.check_cfg(msg='Checking for sdl-config',
                   path='sdl-config',
                   package='',
                   args='--cflags --libs',
                   uselib_store='SDL')

    conf.find_program('objcopy gobjcopy', var='OBJCOPY')

    # We are overriding the gcc toolchain include/time.h with our own
    # just to check/force our version of <time.h>
    conf.env.CFLAGS.append('-D_TIME_H_')


def build(bld):
    sources = bld.path.ant_glob('*.c')
    bld.stlib(source=sources,
              target='applib_sdl',
              includes='.',
              export_includes='.',
              use=['applib', 'fw_includes', 'libutil', 'upng', 'SDL'])
    bld.recurse('examples')

# vim:filetype=python
