<!DOCTYPE html>
<!--
 Copyright 2024 Google LLC

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->

<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Simple Rocky Simulator</title>

    <link rel="stylesheet" href="css/bootstrap.css"/>
    <link rel="stylesheet" href="css/style.css"/>

    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script type="text/javascript" src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
    <script type="text/javascript" src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->

    <script type="text/javascript" src="rocky.js"></script>
</head>
<body>
<div class="container">
    <div class="page-header">
        <h1>[Simple Rocky Simulator]</h1>
    </div>

    <div class="col-md-6">
        <canvas id="pebble" class="rocky" width="432" height="504"></canvas>
    </div>

    <div class="col-md-6">
        <h3>Controls</h3>
        <p><form id="controls-form">
            <label for="timestamp-input">Set Time (Epoch timestamp)</label>
            <input id="timestamp-input" type="text" />
            <label for="timezone-offset-input">Timezone Offset (<span id="timezone-offset-span"></span>)</label>
            <input id="timezone-offset-input" type="range" min="-720" max="840" step="15" value="0" />
            <label>24h Style</label>
            <input id="24h-style-input" type="checkbox" />
        </form></p>
    </div>
</div>
<script type="text/javascript">
    // Create a new simulator and bind it to the canvas:
    var rockySimulator =  new RockySimulator({
        canvas: document.getElementById("pebble")
    });

    // in the future, we will replace the singleton
    // `rocky` as well as the namespace `Rocky`, e.g.
    // `Rocky.tween` and `Rocky.WatchfaceHelper` with modules
    var rocky = _rocky;

    function getQueryVariable(variable) {
        var query = window.location.search.substring(1);
        var vars = query.split('&');
        for (var i = 0; i < vars.length; i++) {
            var pair = vars[i].split('=');
            if (decodeURIComponent(pair[0]) == variable) {
                return decodeURIComponent(pair[1]);
            }
        }
    }

    // Load app source from 'src' query param (URL / data URI):
    var src = getQueryVariable('src');
    // If 'src' is set to empty string, don't load anything (for testing):
    if (src !== '') {
        src  = src || 'js/tictoc.js';
        var script = document.createElement('script');
        script.src = src;
        document.getElementsByTagName('html')[0].appendChild(script);
    }
</script>
<script type="text/javascript" src="js/controls.js"></script>
</body>
</html>
