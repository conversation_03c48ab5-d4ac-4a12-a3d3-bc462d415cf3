/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

@import url(https://fonts.googleapis.com/css?family=Bowlby+One+SC|Roboto:400,700,400italic);

body {
  font: 16px/1.5 'Roboto', sans-serif;
}

body {
  padding-bottom: 2em;
}

body > .container:first-child > .row:first-child > div > p:first-child {
  display: none;
}

h1 {
  font-size: 3em;
}

h1 + blockquote {
  border: 0 none;
  max-width: 600px;
  text-align: center;
  margin: 0 auto 3em;
  padding: 0;
}

h1, h2 {
  font-family: "Bowlby One SC";
  overflow: hidden;
  text-align: center;
  margin: 2em 0 1em;
  text-transform: uppercase;
}

h1:before, h1:after, h2:before, h2:after {
  background-color: #ddd;
  content: "";
  display: inline-block;
  height: 1px;
  position: relative;
  vertical-align: middle;
  width: 50%;
}

h1:before, h2:before {
  right: 0.5em;
  margin-left: -50%;
}

h1:after, h2:after {
  left: 0.5em;
  margin-right: -50%;
}

h3 {
  margin: 1em 0 0.5em;
}

form {
  width: 100%;
}

label {
  width: 100%;
}

#githubForkLink {
  background: url(../img/forkBanner.png) no-repeat top right;
  position: absolute;
  display: block;
  width: 149px;
  height: 149px;
  top: 0;
  right: 0;

  /* Hide the text. */
  text-indent: 100%;
  white-space: nowrap;
  overflow: hidden;
}

table.table-compatibility>tbody>tr.standard {
  background-color: #d9edf7;
}

table.table-compatibility>tbody>tr.implemented {
  background-color: #dff0db;
}

table.table-compatibility>tbody>tr.partial {
  background-color: #ffffff;
}

table.table-compatibility>tbody>tr.planned {
  background-color: #fcf8e3;
}

table.table-compatibility>tbody>tr.not-planned {
  background-color: #f2dede
}
