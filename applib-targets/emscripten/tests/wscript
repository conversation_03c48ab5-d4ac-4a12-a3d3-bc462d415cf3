from waflib import Logs


def configure(conf):
    conf.load('gcc waf_unit_test')
    pass


def show_results(bld):
    lst = getattr(bld, 'utest_results', [])
    if lst:
        Logs.pprint('CYAN', 'execution summary')

        total = len(lst)
        tfail = len([x for x in lst if x[1]])

        Logs.pprint('CYAN', '  tests that pass %d/%d' % (total-tfail, total))
        for (f, code, out, err) in lst:
            if not code:
                Logs.pprint('CYAN', '    %s' % f)

        if (tfail):
            Logs.pprint('RED', '  tests that fail %d/%d' % (tfail, total))
            for (f, code, out, err) in lst:
                if code:
                    Logs.pprint('CYAN', '    %s' % f)
                    Logs.pprint('WHITE', '        %s' % out)


def build(bld):
    includes = ['.', '../', '../../../src/fw/']
    sources = bld.path.ant_glob('*.c')
    sources.append(bld.path.parent.find_node('emscripten_resources.c'))
    bld.program(features='test',
                source=sources,
                target='test',
                cflags='-g',
                includes=includes)

    bld.add_post_fun(show_results)

# vim:filetype=python
