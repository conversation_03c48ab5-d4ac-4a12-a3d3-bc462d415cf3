{"_args": [[{"raw": "timeshift-js@^1.0.0", "scope": null, "escapedName": "timeshift-js", "name": "timeshift-js", "rawSpec": "^1.0.0", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "/Users/<USER>/Documents/Pebble/pebblesdk-generator/basalt/applib-targets/emscripten"]], "_from": "timeshift-js@>=1.0.0 <2.0.0", "_id": "timeshift-js@1.0.0", "_inCache": true, "_installable": true, "_location": "/timeshift-js", "_nodeVersion": "5.3.0", "_npmUser": {"name": "plaa", "email": "<EMAIL>"}, "_npmVersion": "3.3.12", "_phantomChildren": {}, "_requested": {"raw": "timeshift-js@^1.0.0", "scope": null, "escapedName": "timeshift-js", "name": "timeshift-js", "rawSpec": "^1.0.0", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/timeshift-js/-/timeshift-js-1.0.0.tgz", "_shasum": "61c2eebc12e9dabc81e5f99bbae2dc8d14593f70", "_shrinkwrap": null, "_spec": "timeshift-js@^1.0.0", "_where": "/Users/<USER>/Documents/Pebble/pebblesdk-generator/basalt/applib-targets/emscripten", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/plaa/TimeShift-js/issues"}, "dependencies": {}, "description": "Time and timezone mocking / overriding", "devDependencies": {}, "directories": {}, "dist": {"shasum": "61c2eebc12e9dabc81e5f99bbae2dc8d14593f70", "tarball": "https://registry.npmjs.org/timeshift-js/-/timeshift-js-1.0.0.tgz"}, "gitHead": "2b0ae910e28ebbe40482c13ba48dc5c54adfe2b7", "homepage": "https://github.com/plaa/TimeShift-js", "keywords": ["time", "timezone", "mocking"], "license": "MIT", "main": "timeshift.js", "maintainers": [{"name": "plaa", "email": "<EMAIL>"}], "name": "timeshift-js", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/plaa/TimeShift-js.git"}, "scripts": {}, "version": "1.0.0"}