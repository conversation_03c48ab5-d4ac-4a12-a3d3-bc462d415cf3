/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include <stdlib.h>
#include <string.h>

static void* applib_zalloc(size_t size) {
  void* result = malloc(size);
  if (result) {
    memset(result, 0, size);
  }
  return result;
}

#define applib_type_zalloc(Type) applib_zalloc(sizeof(Type))
#define applib_type_malloc(Type) malloc(sizeof(Type))
#define applib_type_size(Type) sizeof(Type)
#define applib_malloc(size) malloc(size)
#define applib_free(ptr) free(ptr)
