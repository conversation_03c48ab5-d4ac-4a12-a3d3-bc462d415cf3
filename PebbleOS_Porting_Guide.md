# PebbleOS Porting Guide for Ambiq Apollo 4 Blue Plus

## Executive Summary

This document provides a comprehensive strategy for porting PebbleOS components to a FreeRTOS-based system running on the Ambiq Apollo 4 Blue Plus, with a focus on UI-related features. **Critical insight: The core system architecture must be ported first**, as the UI system has deep dependencies on task management, memory allocation, and event handling.

## PebbleOS Architecture Analysis

### Core System Components

PebbleOS is built on a sophisticated multi-layered architecture:

1. **FreeRTOS Kernel** with custom task management
2. **Multi-process architecture** (Apps, Workers, System processes)
3. **Task-aware memory management** with separate heaps
4. **Event-driven system** with comprehensive routing
5. **Hardware abstraction layer** supporting multiple MCU families
6. **Memory protection** with MPU-based process isolation

### Key Features Identified

#### Graphics and UI System
- **Multi-platform display drivers**: Sharp Memory LCD, E-ink, Color displays
- **Framebuffer management** with double buffering
- **Graphics context system** with clipping and transformations
- **Layer-based UI hierarchy**: Root → Window → Child layers
- **Rich UI components**: Text, Bitmap, Menu, Action Bar, Progress, Scroll layers
- **Animation system**: Property animations with timing curves
- **Drawing primitives**: Lines, rectangles, circles, paths with anti-aliasing

#### Input and Hardware Services
- **Button handling** with debouncing and click recognition
- **Sensor integration**: Accelerometer, compass, heart rate
- **Power management**: Battery monitoring, backlight control
- **Communication**: Bluetooth LE/Classic, app messaging

#### App Framework
- **Process isolation** with memory protection
- **App lifecycle management**: Load, run, suspend, terminate
- **Resource management**: Fonts, images, persistent storage
- **Inter-process communication**: Events, messages, data logging

## Critical Dependencies Analysis

### Why Core System Must Come First

The UI system has **hard dependencies** on core architecture:

#### 1. Task-Aware Memory Management
```c
Heap *task_heap_get_for_current_task(void) {
  if (pebble_task_get_current() == PebbleTask_App) {
    return app_state_get_heap();
  } else if (pebble_task_get_current() == PebbleTask_Worker) {
    return worker_state_get_heap();
  }
  return kernel_heap_get();
}
```
**Impact**: Graphics allocation fails without task context identification.

#### 2. Graphics Context Dependencies
```c
GContext *graphics_context_get_current_context(void) {
  if (pebble_task_get_current() == PebbleTask_App) {
    return app_state_get_graphics_context();
  } else {
    return kernel_ui_get_graphics_context();
  }
}
```
**Impact**: Graphics rendering requires task-specific context selection.

#### 3. Event System Integration
```c
static void prv_handle_event(PebbleEvent *e) {
  prv_minimal_event_handler(e);
  // Event routing based on task context
  shell_event_loop_handle_event(e);
}
```
**Impact**: UI events cannot be routed without core event infrastructure.

#### 4. Memory Protection
```c
static const MpuRegion s_app_region = {
  .region_num = MemoryRegion_AppRAM,
  .base_address = MPU_REGION_APP_BASE_ADDRESS,
  .size = MPU_REGION_APP_SIZE,
  .priv_read = true,
  .priv_write = true,
};
```
**Impact**: UI operates within protected memory regions.

### Dependency Chain
```
UI Components → Graphics Context → Memory Allocation → Task Management
     ↓               ↓                    ↓                 ↓
Event Handling → Event System → Process State → Task Context
```

## Revised Porting Strategy: Core-First Approach

### PHASE 1: Core System Foundation (Weeks 1-6) ⭐⭐⭐⭐⭐

#### 1.1 Task Management System (Weeks 1-2)
**Priority: CRITICAL - Foundation for everything**

**Components to port:**
- `src/fw/kernel/pebble_tasks.c` - Task identification and management
- `src/fw/kernel/pebble_tasks.h` - Task enumeration and interfaces

**Key files:**
- Task enumeration (PebbleTask_KernelMain, PebbleTask_App, etc.)
- Task creation with memory protection
- Current task identification

**Implementation approach:**
```c
typedef enum {
  PebbleTask_KernelMain,
  PebbleTask_App,
  PebbleTask_Worker,
  // Add others as needed
} PebbleTask;

PebbleTask pebble_task_get_current(void);
void pebble_task_create(PebbleTask task, TaskParameters_t *params, TaskHandle_t *handle);
```

**Success criteria:**
- Task identification works correctly
- Task creation with proper parameters
- Task switching maintains context

#### 1.2 Memory Management (Weeks 2-3)
**Priority: CRITICAL - Required for any allocation**

**Components to port:**
- `src/fw/kernel/kernel_heap.c` - Kernel heap management
- `src/fw/kernel/pbl_malloc.c` - Task-aware memory allocation
- `src/fw/util/heap.c` - Core heap implementation
- `src/fw/kernel/memory_layout.c` - Memory region setup

**Key features:**
- Separate heaps for kernel, app, and worker tasks
- Task-aware allocation functions (app_malloc, kernel_malloc)
- Memory protection and bounds checking
- Heap corruption detection

**Implementation approach:**
```c
// Task-aware allocation
void *app_malloc(size_t bytes);
void *kernel_malloc(size_t bytes);
void app_free(void *ptr);
void kernel_free(void *ptr);

// Heap management
Heap *task_heap_get_for_current_task(void);
void heap_init(Heap *heap, void *start, void *end, bool enable_fuzzing);
```

**Success criteria:**
- Multiple heaps working independently
- Task-aware allocation routing correctly
- Memory protection preventing cross-task access
- Heap corruption detection functional

#### 1.3 Event System Core (Weeks 3-4)
**Priority: CRITICAL - Required for UI interaction**

**Components to port:**
- `src/fw/kernel/events.c` - Event structure and basic handling
- `src/fw/kernel/event_loop.c` - Main event loop (simplified)
- Event queues and routing infrastructure

**Key features:**
- PebbleEvent structure and types
- Event queues for different tasks
- Basic event routing and handling
- Event cleanup and memory management

**Implementation approach:**
```c
typedef struct PebbleEvent {
  PebbleEventType type;
  PebbleTaskBitset task_mask;
  union {
    PebbleButtonEvent button;
    PebbleRenderEvent render;
    // Other event types
  };
} PebbleEvent;

void event_put(PebbleEvent *event);
bool event_take_timeout(PebbleEvent *event, uint32_t timeout_ms);
void event_cleanup(PebbleEvent *event);
```

**Success criteria:**
- Events can be created and queued
- Event routing works between tasks
- Event cleanup prevents memory leaks
- Basic event loop operational

#### 1.4 Process State Management (Weeks 4-6)
**Priority: HIGH - Required for app/kernel separation**

**Components to port:**
- `src/fw/process_state/app_state/app_state.c` - App state management
- Basic process context switching
- App heap initialization and management

**Key features:**
- App state structure and lifecycle
- Process context switching
- App-specific resource management
- State persistence across context switches

**Success criteria:**
- App state can be created and destroyed
- Context switching preserves state correctly
- App heap properly isolated
- Resource cleanup on app termination

### PHASE 2: Hardware Abstraction (Weeks 7-8) ⭐⭐⭐☆☆

#### 2.1 Basic Driver Framework (Week 7)
**Components to port:**
- GPIO abstraction for Apollo 4 Blue Plus
- Basic peripheral configuration
- Interrupt handling framework

**Apollo 4 specific adaptations:**
- GPIO pin mapping and configuration
- Timer peripheral integration
- Power management integration

#### 2.2 Display Driver Integration (Week 8)
**Components to port:**
- `src/fw/drivers/display/display.h` - Display interface
- SPI/I2C drivers for your specific display
- Basic display operations (init, clear, update)

**Implementation approach:**
```c
void display_init(void);
void display_clear(void);
void display_update(NextRowCallback nrcb, UpdateCompleteCallback uccb);
bool display_update_in_progress(void);
```

### PHASE 3: Graphics Foundation (Weeks 9-12) ⭐⭐⭐☆☆

#### 3.1 Graphics Context System (Weeks 9-10)
**Now that we have task management:**

**Components to port:**
- `src/fw/applib/graphics/graphics.c` - Graphics context
- `src/fw/applib/graphics/framebuffer.c` - Framebuffer management
- `src/fw/kernel/ui/kernel_ui.c` - Kernel UI integration

**Key features:**
- Task-aware graphics context allocation
- Framebuffer management with double buffering
- Graphics state management (colors, clipping, etc.)

**Implementation approach:**
```c
typedef struct GContext {
  GBitmap dest_bitmap;
  FrameBuffer *parent_framebuffer;
  GDrawState draw_state;
  bool lock;
} GContext;

void graphics_context_init(GContext *ctx, FrameBuffer *fb, GContextInitializationMode mode);
GContext *graphics_context_get_current_context(void);
```

#### 3.2 Basic Drawing Primitives (Weeks 11-12)
**Components to port:**
- `src/fw/applib/graphics/graphics_private.c` - Low-level drawing
- Pixel, line, rectangle drawing functions
- Clipping and bounds checking

**Success criteria:**
- Basic shapes can be drawn
- Clipping works correctly
- Multiple graphics contexts don't interfere
- Drawing operations are task-aware

### PHASE 4: UI Framework (Weeks 13-20) ⭐⭐⭐⭐☆

#### 4.1 Layer System (Weeks 13-14)
**Components to port:**
- `src/fw/applib/ui/layer.c` - Layer hierarchy
- `src/fw/applib/ui/layer_private.h` - Layer internals
- Layer rendering pipeline

#### 4.2 Window System (Weeks 15-16)
**Components to port:**
- `src/fw/applib/ui/window.c` - Window management
- `src/fw/applib/ui/window_stack.c` - Window stack operations
- `src/fw/applib/ui/app_window_stack.c` - App window integration

#### 4.3 Text Rendering (Weeks 17-18)
**Components to port:**
- `src/fw/applib/fonts/fonts.c` - Font system
- `src/fw/applib/ui/text_layer.c` - Text layer implementation
- `src/fw/applib/graphics/text.c` - Text rendering

#### 4.4 Input Integration (Weeks 19-20)
**Components to port:**
- `src/fw/drivers/button.c` - Button driver (adapted for Apollo 4)
- `src/fw/applib/ui/click.c` - Click recognizers
- Event routing to UI components

## Implementation Guidelines

### Apollo 4 Blue Plus Specific Considerations

#### Hardware Adaptations Required:
1. **GPIO Configuration**: Adapt button handling from STM32/nRF52 examples
2. **SPI/I2C Drivers**: Implement for your specific display interface
3. **Timer Integration**: Use Apollo 4's timer peripherals for animations
4. **Memory Layout**: Adapt for Apollo 4's memory map and MPU
5. **Power Management**: Integrate with Apollo 4's low-power features

#### Memory Requirements:
- **Kernel Heap**: 50-100KB for system operations
- **App Heap**: 50-200KB per application
- **Framebuffer**: Depends on display (e.g., 144x168 = ~3KB for 1-bit)
- **Font Storage**: 10-50KB depending on font set
- **Stack Space**: 2KB kernel main, 1.5KB background, 1.4KB per app

#### Performance Optimizations:
1. Use Apollo 4's hardware acceleration where available
2. Implement efficient framebuffer updates (dirty regions)
3. Optimize font rendering with caching
4. Use DMA for display transfers
5. Leverage Apollo 4's low-power modes

### Testing Strategy

#### Phase 1 Testing:
- Task identification correctness
- Memory allocation isolation
- Event routing accuracy
- Process state persistence

#### Phase 2 Testing:
- Hardware driver functionality
- Display basic operations
- Interrupt handling reliability

#### Phase 3 Testing:
- Graphics context switching
- Drawing primitive accuracy
- Framebuffer integrity

#### Phase 4 Testing:
- Layer hierarchy rendering
- Window stack operations
- Text rendering quality
- Input responsiveness

## Success Metrics

### Phase 1 Success:
- Multiple tasks running with proper isolation
- Memory allocation working per-task
- Events routing correctly
- Process states maintained

### Phase 2 Success:
- Hardware drivers operational
- Display showing basic output
- Interrupts handled properly

### Phase 3 Success:
- Graphics primitives working
- Multiple contexts not interfering
- Framebuffer updates smooth

### Phase 4 Success:
- Complete UI hierarchy functional
- Text rendering properly
- Input events reaching UI
- Basic apps can be created

## Risk Mitigation

### High-Risk Areas:
1. **Memory Protection**: MPU configuration for Apollo 4
2. **Task Switching**: Context preservation during switches
3. **Event Timing**: Real-time event handling requirements
4. **Display Interface**: Hardware-specific communication

### Mitigation Strategies:
1. Start with simplified versions of complex systems
2. Implement comprehensive logging for debugging
3. Create test harnesses for each component
4. Maintain compatibility layers for easier porting

## Conclusion

This core-first approach ensures a solid foundation for the sophisticated UI features you want to port. While it requires more upfront investment in system architecture, it will result in a much more robust and maintainable system that can properly support PebbleOS's advanced UI capabilities.

The key insight is that PebbleOS's UI power comes not just from the graphics code, but from the sophisticated system architecture that supports it. By porting this architecture first, you'll have a platform capable of supporting the full range of PebbleOS features.

## Next Steps

1. **Review this document** and identify any specific areas needing clarification
2. **Set up development environment** for Apollo 4 Blue Plus
3. **Start with Phase 1.1** - Task Management System
4. **Create test harnesses** for each component as you port it
5. **Document adaptations** specific to Apollo 4 hardware
6. **Plan integration points** between phases

This document serves as your roadmap - each phase can be broken down into specific, actionable tasks that can be executed systematically.