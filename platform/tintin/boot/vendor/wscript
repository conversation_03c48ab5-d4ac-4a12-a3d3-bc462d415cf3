def build(bld):
    stm32_basedir = 'STM32F2xx_StdPeriph_Lib_V1.1.0/'
    stm32_srcdirs = [stm32_basedir + subpath for subpath in (
        '', 'Libraries/STM32F2xx_StdPeriph_Driver/src',
        'Libraries/CMSIS/CM3/CoreSupport')]
    stm32_sources = sum(
        [bld.path.ant_glob('%s/*.c' % d) for d in stm32_srcdirs], [])

    stm32_incpath_base = stm32_basedir + 'Libraries/'
    stm32_includes = [stm32_incpath_base + subpath for subpath in (
        'CMSIS/Include', 'CMSIS/Device/ST/STM32F2xx/Include',
        'STM32F2xx_StdPeriph_Driver/inc')]

    stm32_includes += ['stm32_conf']

    bld.stlib(source=stm32_sources,
              cflags='-fno-lto',
              target='stm32_stdlib',
              includes=stm32_includes,
              export_includes=stm32_includes)

# vim:filetype=python
