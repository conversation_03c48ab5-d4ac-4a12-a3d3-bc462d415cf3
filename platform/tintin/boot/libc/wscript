def build(bld):
    # Collect all the files in the libc directory
    source_dirs = ['string', 'math', 'stub']
    sources = sum([bld.path.ant_glob('%s/*.c' % d) for d in source_dirs], [])
    sources.extend(bld.path.ant_glob('*.c'))

    # Build the libc directory using firmware environment
    bld.objects(source=sources,
                target='pblibc',
                includes=['include', '.'],
                cflags=['-fno-lto'],
                export_includes='include',
                use='')

# vim:filetype=python
