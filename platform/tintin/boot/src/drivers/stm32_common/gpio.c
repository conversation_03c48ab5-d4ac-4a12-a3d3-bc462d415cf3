/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "drivers/gpio.h"

#include <stdint.h>

#define MAX_GPIO (9)


static uint32_t s_gpio_clock_count[MAX_GPIO];

void gpio_use(GPIO_TypeDef* GPIOx) {
  uint32_t idx = ((((uint32_t)GPIOx) - AHB1PERIPH_BASE) / 0x0400);
  if ((idx < MAX_GPIO) && !(s_gpio_clock_count[idx]++)) {
    SET_BIT(RCC->AHB1ENR, (0x1 << idx));
  }
}

void gpio_release(GPIO_TypeDef* GPIOx) {
  uint32_t idx = ((((uint32_t)GPIOx) - AHB1PERIPH_BASE) / 0x0400);
  if ((idx < MAX_GPIO) && s_gpio_clock_count[idx] && !(--s_gpio_clock_count[idx])) {
    CLEAR_BIT(RCC->AHB1ENR, (0x1 << idx));
  }
}
