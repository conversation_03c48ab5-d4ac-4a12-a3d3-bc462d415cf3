/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

//! @addtogroup UI
//! @{
//!   @addtogroup Clicks
//!   \brief Dealing with button input
//!   @{

//! Button ID values
//! @see \ref click_recognizer_get_button_id()
typedef enum {
  //! Back button
  BUTTON_ID_BACK = 0,
  //! Up button
  BUTTON_ID_UP,
  //! Select (middle) button
  BUTTON_ID_SELECT,
  //! Down button
  BUTTON_ID_DOWN,
  //! Total number of buttons
  NUM_BUTTONS
} ButtonId;

//!   @} // end addtogroup Clicks
//! @} // end addtogroup UI
