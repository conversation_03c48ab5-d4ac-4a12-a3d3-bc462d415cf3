/*
Linker script for STM32F2xx_1024K_128K
*/

__Stack_Size = 8192;

/* Memory Spaces Definitions */

MEMORY
{
  RAM  (xrw) : ORIGIN = 0x20000000, LENGTH = 128K
  FLASH (rx) : ORIGIN = 0x08000000, LENGTH = @BOOTLOADER_LENGTH@
}

/* include the sections management sub-script for FLASH mode */

/* Sections Definitions */

SECTIONS
{
    /* for Cortex devices, the beginning of the startup code is stored in the .isr_vector section, which goes to FLASH */
    .isr_vector :
    {
        KEEP(*(.isr_vector))            /* Startup code */
    } >FLASH

    /* Exception handling sections. "contains index entries for section unwinding"
     * We don't actually use this or know what it is, but it seems happy to go here. */
    .ARM.exidx :
    {   
        *(.ARM.exidx)
    } >FLASH

    /* Exported symbols:
     * This list can be calculated by putting all the KEEP directives in the normal
     * .text section, then using nm to find the addresses of each symbol. Then we
     * put each exported symbol in its own section so we can control their addresses */

    .text.memcpy 0x08000048 : { KEEP(*(.text.memcpy)) } > FLASH
    .text.memset 0x0800005a : { KEEP(*(.text.memset)) } > FLASH
    .text.memchr 0x0800006a : { KEEP(*(.text.memchr)) } > FLASH
    .text.strcat 0x08000086 : { KEEP(*(.text.strcat)) } > FLASH
    .text.strchr 0x0800009c : { KEEP(*(.text.strchr)) } > FLASH
    .text.strcmp 0x080000b2 : { KEEP(*(.text.strcmp)) } > FLASH
    .text.strcpy 0x080000c8 : { KEEP(*(.text.strcpy)) } > FLASH
    .text.strlen 0x080000e0 : { KEEP(*(.text.strlen)) } > FLASH
    .text.strnlen 0x080000ee : { KEEP(*(.text.strnlen)) } > FLASH
    .text.strspn 0x08000100 : { KEEP(*(.text.strspn)) } > FLASH
    .text.strtol 0x0800011c : { KEEP(*(.text.strtol)) } > FLASH
    .text.atoi 0x08000140 : { KEEP(*(.text.atoi)) } > FLASH
    .text.atol 0x08000168 : { KEEP(*(.text.atol)) } > FLASH
    .text.puts 0x08000190 : { KEEP(*(.text.puts)) } > FLASH
    .text.exit 0x08000194 : { KEEP(*(.text.exit)) } > FLASH
    .text.strstr 0x08000196 : { KEEP(*(.text.strstr)) } > FLASH
    .text.strcspn 0x080001c2 : { KEEP(*(.text.strcspn)) } > FLASH
    .text.strncpy 0x080001e0 : { KEEP(*(.text.strncpy)) } > FLASH
    .text.strncmp 0x08000214 : { KEEP(*(.text.strncmp)) } > FLASH
    .text.strrchr 0x08000232 : { KEEP(*(.text.strrchr)) } > FLASH
    .text.strncat 0x08000252 : { KEEP(*(.text.strncat)) } > FLASH
    .text.memmove 0x0800027e : { KEEP(*(.text.memmove)) } > FLASH
    .text.memcmp 0x080002a2 : { KEEP(*(.text.memcmp)) } > FLASH
    .text.vsnprintf 0x080002bc : { KEEP(*(.text.vsnprintf)) } > FLASH
    .text.snprintf 0x08000730 : { KEEP(*(.text.snprintf)) } > FLASH
    .text.vsprintf 0x0800074a : { KEEP(*(.text.vsprintf)) } > FLASH
    .text.sprintf 0x0800075c : { KEEP(*(.text.sprintf)) } > FLASH
    .text.setlocale 0x08000776 : { KEEP(*(.text.setlocale)) } > FLASH
    .text.RCC_ClearFlag 0x0800077c : { KEEP(*(.text.RCC_ClearFlag)) } > FLASH
    .text.TIM_Cmd 0x0800078c : { KEEP(*(.text.TIM_Cmd)) } > FLASH
    .text.CRC_ResetDR 0x08000810 : { KEEP(*(.text.CRC_ResetDR)) } > FLASH
    .text.CRC_CalcCRC 0x0800081c : { KEEP(*(.text.CRC_CalcCRC)) } > FLASH
    .text.CRC_CalcBlockCRC 0x08000828 : { KEEP(*(.text.CRC_CalcBlockCRC)) } > FLASH
    .text.CRC_GetCRC 0x08000844 : { KEEP(*(.text.CRC_GetCRC)) } > FLASH
    .text.DBGMCU_APB1PeriphConfig 0x08000850 : { KEEP(*(.text.DBGMCU_APB1PeriphConfig)) } > FLASH
    .text.FLASH_Unlock 0x08000868 : { KEEP(*(.text.FLASH_Unlock)) } > FLASH
    .text.FLASH_Lock 0x08000884 : { KEEP(*(.text.FLASH_Lock)) } > FLASH
    .text.FLASH_ClearFlag 0x08000894 : { KEEP(*(.text.FLASH_ClearFlag)) } > FLASH
    .text.FLASH_GetStatus 0x080008a0 : { KEEP(*(.text.FLASH_GetStatus)) } > FLASH
    .text.FLASH_EraseSector 0x080008d4 : { KEEP(*(.text.FLASH_EraseSector)) } > FLASH
    .text.FLASH_ProgramByte 0x08000944 : { KEEP(*(.text.FLASH_ProgramByte)) } > FLASH
    .text.IWDG_WriteAccessCmd 0x0800097c : { KEEP(*(.text.IWDG_WriteAccessCmd)) } > FLASH
    .text.IWDG_SetPrescaler 0x08000988 : { KEEP(*(.text.IWDG_SetPrescaler)) } > FLASH
    .text.IWDG_SetReload 0x08000994 : { KEEP(*(.text.IWDG_SetReload)) } > FLASH
    .text.IWDG_ReloadCounter 0x080009a0 : { KEEP(*(.text.IWDG_ReloadCounter)) } > FLASH
    .text.IWDG_Enable 0x080009b0 : { KEEP(*(.text.IWDG_Enable)) } > FLASH
    .text.PWR_BackupAccessCmd 0x080009c0 : { KEEP(*(.text.PWR_BackupAccessCmd)) } > FLASH
    .text.PWR_WakeUpPinCmd 0x080009cc : { KEEP(*(.text.PWR_WakeUpPinCmd)) } > FLASH
    .text.PWR_GetFlagStatus 0x080009d8 : { KEEP(*(.text.PWR_GetFlagStatus)) } > FLASH
    .text.PWR_ClearFlag 0x080009ec : { KEEP(*(.text.PWR_ClearFlag)) } > FLASH
    .text.RCC_DeInit 0x080009fc : { KEEP(*(.text.RCC_DeInit)) } > FLASH
    .text.RCC_LSEConfig 0x08000a30 : { KEEP(*(.text.RCC_LSEConfig)) } > FLASH
    .text.RCC_GetClocksFreq 0x08000a50 : { KEEP(*(.text.RCC_GetClocksFreq)) } > FLASH
    .text.RCC_RTCCLKConfig 0x08000ad8 : { KEEP(*(.text.RCC_RTCCLKConfig)) } > FLASH
    .text.RCC_RTCCLKCmd 0x08000b08 : { KEEP(*(.text.RCC_RTCCLKCmd)) } > FLASH
    .text.RCC_AHB1PeriphClockCmd 0x08000b14 : { KEEP(*(.text.RCC_AHB1PeriphClockCmd)) } > FLASH
    .text.RCC_APB1PeriphClockCmd 0x08000b2c : { KEEP(*(.text.RCC_APB1PeriphClockCmd)) } > FLASH
    .text.RCC_APB2PeriphClockCmd 0x08000b44 : { KEEP(*(.text.RCC_APB2PeriphClockCmd)) } > FLASH
    .text.RCC_AHB1PeriphResetCmd 0x08000b5c : { KEEP(*(.text.RCC_AHB1PeriphResetCmd)) } > FLASH
    .text.RCC_AHB2PeriphResetCmd 0x08000b74 : { KEEP(*(.text.RCC_AHB2PeriphResetCmd)) } > FLASH
    .text.RCC_AHB3PeriphResetCmd 0x08000b8c : { KEEP(*(.text.RCC_AHB3PeriphResetCmd)) } > FLASH
    .text.RCC_APB1PeriphResetCmd 0x08000ba4 : { KEEP(*(.text.RCC_APB1PeriphResetCmd)) } > FLASH
    .text.RCC_APB2PeriphResetCmd 0x08000bbc : { KEEP(*(.text.RCC_APB2PeriphResetCmd)) } > FLASH
    .text.RCC_GetFlagStatus 0x08000bd4 : { KEEP(*(.text.RCC_GetFlagStatus)) } > FLASH
    .text.RTC_EnterInitMode 0x08000bfc : { KEEP(*(.text.RTC_EnterInitMode)) } > FLASH
    .text.RTC_ExitInitMode 0x08000c3c : { KEEP(*(.text.RTC_ExitInitMode)) } > FLASH
    .text.RTC_Init 0x08000c4c : { KEEP(*(.text.RTC_Init)) } > FLASH
    .text.RTC_WaitForSynchro 0x08000c90 : { KEEP(*(.text.RTC_WaitForSynchro)) } > FLASH
    .text.RTC_SetTime 0x08000cd4 : { KEEP(*(.text.RTC_SetTime)) } > FLASH
    .text.RTC_GetTime 0x08000d64 : { KEEP(*(.text.RTC_GetTime)) } > FLASH
    .text.RTC_SetDate 0x08000da8 : { KEEP(*(.text.RTC_SetDate)) } > FLASH
    .text.RTC_GetDate 0x08000e2c : { KEEP(*(.text.RTC_GetDate)) } > FLASH
    .text.RTC_TimeStampCmd 0x08000e6c : { KEEP(*(.text.RTC_TimeStampCmd)) } > FLASH
    .text.RTC_WriteBackupRegister 0x08000e94 : { KEEP(*(.text.RTC_WriteBackupRegister)) } > FLASH
    .text.RTC_ReadBackupRegister 0x08000eb4 : { KEEP(*(.text.RTC_ReadBackupRegister)) } > FLASH
    .text.RTC_TimeStampPinSelection 0x08000ed4 : { KEEP(*(.text.RTC_TimeStampPinSelection)) } > FLASH
    .text.RTC_ITConfig 0x08000eec : { KEEP(*(.text.RTC_ITConfig)) } > FLASH
    .text.RTC_GetFlagStatus 0x08000f28 : { KEEP(*(.text.RTC_GetFlagStatus)) } > FLASH
    .text.RTC_ClearFlag 0x08000f44 : { KEEP(*(.text.RTC_ClearFlag)) } > FLASH
    .text.SPI_I2S_DeInit 0x08000f60 : { KEEP(*(.text.SPI_I2S_DeInit)) } > FLASH
    .text.USART_Init 0x08000fbc : { KEEP(*(.text.USART_Init)) } > FLASH
    .text.FLASH_WaitForLastOperation 0x0800106c : { KEEP(*(.text.FLASH_WaitForLastOperation)) } > FLASH
    .text.GPIO_Init 0x0800108e : { KEEP(*(.text.GPIO_Init)) } > FLASH
    .text.GPIO_StructInit 0x0800110e : { KEEP(*(.text.GPIO_StructInit)) } > FLASH
    .text.GPIO_ReadInputDataBit 0x08001120 : { KEEP(*(.text.GPIO_ReadInputDataBit)) } > FLASH
    .text.GPIO_SetBits 0x0800112c : { KEEP(*(.text.GPIO_SetBits)) } > FLASH
    .text.GPIO_ResetBits 0x08001130 : { KEEP(*(.text.GPIO_ResetBits)) } > FLASH
    .text.GPIO_WriteBit 0x08001134 : { KEEP(*(.text.GPIO_WriteBit)) } > FLASH
    .text.GPIO_PinAFConfig 0x0800113e : { KEEP(*(.text.GPIO_PinAFConfig)) } > FLASH
    .text.RTC_ByteToBcd2 0x08001162 : { KEEP(*(.text.RTC_ByteToBcd2)) } > FLASH
    .text.RTC_Bcd2ToByte 0x0800117c : { KEEP(*(.text.RTC_Bcd2ToByte)) } > FLASH
    .text.RTC_StructInit 0x0800118e : { KEEP(*(.text.RTC_StructInit)) } > FLASH
    .text.RTC_TimeStructInit 0x0800119a : { KEEP(*(.text.RTC_TimeStructInit)) } > FLASH
    .text.RTC_DateStructInit 0x080011a6 : { KEEP(*(.text.RTC_DateStructInit)) } > FLASH
    .text.SPI_Init 0x080011b4 : { KEEP(*(.text.SPI_Init)) } > FLASH
    .text.SPI_Cmd 0x080011f2 : { KEEP(*(.text.SPI_Cmd)) } > FLASH
    .text.SPI_I2S_ReceiveData 0x0800120a : { KEEP(*(.text.SPI_I2S_ReceiveData)) } > FLASH
    .text.SPI_I2S_SendData 0x08001210 : { KEEP(*(.text.SPI_I2S_SendData)) } > FLASH
    .text.SPI_I2S_GetFlagStatus 0x08001214 : { KEEP(*(.text.SPI_I2S_GetFlagStatus)) } > FLASH
    .text.USART_Cmd 0x08001220 : { KEEP(*(.text.USART_Cmd)) } > FLASH
    .text.USART_SendData 0x08001238 : { KEEP(*(.text.USART_SendData)) } > FLASH
    .text.USART_GetFlagStatus 0x08001240 : { KEEP(*(.text.USART_GetFlagStatus)) } > FLASH
    .text.ctype_data 0x0800124c : { KEEP(*(.rodata.__ctype_data)) } > FLASH
    .text.bpapi 0x0800134c : { *(libgcc.a:bpapi.o*) } > FLASH
    .text.uldivmod 0x08001378 : { *(libgcc.a:_aeabi_uldivmod.o*) } > FLASH
    .text.dvmd 0x080013d8 : { *(libgcc.a:_dvmd_tls.o*) } > FLASH
    .text.divdi3 0x080013dc : { *(libgcc.a:_divdi3.o*) } > FLASH
    .text.udivdi3 0x0800167c : { *(libgcc.a:_udivdi3.o*) } > FLASH

    /* the program code is stored in the .text section, which goes to Flash */
    .text : {
        *(.text)                   /* remaining code */
        *(.text.*)                   /* remaining code */
        *(.rodata)                 /* read-only data (constants) */
        *(.rodata*)
        *(.constdata)                 /* read-only data (constants) */
        *(.constdata*)
        . = ALIGN(4);
    } >FLASH

    /* This is the initialized data section
    The program executes knowing that the data is in the RAM
    but the loader puts the initial values in the FLASH (inidata).
    It is one task of the startup to copy the initial values from FLASH to RAM. */
    .data : {
        . = ALIGN(4);
        /* This is used by the startup in order to initialize the .data secion */
        __data_start = .;

        *(.data)
        *(.data.*)

        . = ALIGN(4);
        __data_end = .; /* This is used by the startup in order to initialize the .data secion */
    } >RAM AT>FLASH
    __data_load_start = LOADADDR(.data);

    /* This is the uninitialized data section */
    .bss (NOLOAD) :
    {
        . = ALIGN(4);
        __bss_start = .; /* This is used by the startup in order to initialize the .bss secion */

        *(.bss)
        *(.bss.*)
        *(COMMON)

        . = ALIGN(4);
        __bss_end = .; /* This is used by the startup in order to initialize the .bss secion */
    } >RAM

    .stack (NOLOAD) :
    {
        . = ALIGN(8);
        . += __Stack_Size;
        _estack = .;
    } >RAM

    /* after that it's only debugging information. */
    /* remove the debugging information from the standard libraries */
    DISCARD : {
        libgcc.a ( * )
    }

    /* Stabs debugging sections.  */
    .stab          0 : { *(.stab) }
    .stabstr       0 : { *(.stabstr) }
    .stab.excl     0 : { *(.stab.excl) }
    .stab.exclstr  0 : { *(.stab.exclstr) }
    .stab.index    0 : { *(.stab.index) }
    .stab.indexstr 0 : { *(.stab.indexstr) }
    .comment       0 : { *(.comment) }
    /* DWARF debug sections.
       Symbols in the DWARF debugging sections are relative to the beginning
       of the section so we begin them at 0.  */
    /* DWARF 1 */
    .debug          0 : { *(.debug) }
    .line           0 : { *(.line) }
    /* GNU DWARF 1 extensions */
    .debug_srcinfo  0 : { *(.debug_srcinfo) }
    .debug_sfnames  0 : { *(.debug_sfnames) }
    /* DWARF 1.1 and DWARF 2 */
    .debug_aranges  0 : { *(.debug_aranges) }
    .debug_pubnames 0 : { *(.debug_pubnames) }
    /* DWARF 2 */
    .debug_info     0 : { *(.debug_info .gnu.linkonce.wi.*) }
    .debug_abbrev   0 : { *(.debug_abbrev) }
    .debug_line     0 : { *(.debug_line) }
    .debug_frame    0 : { *(.debug_frame) }
    .debug_str      0 : { *(.debug_str) }
    .debug_loc      0 : { *(.debug_loc) }
    .debug_macinfo  0 : { *(.debug_macinfo) }
    /* SGI/MIPS DWARF 2 extensions */
    .debug_weaknames 0 : { *(.debug_weaknames) }
    .debug_funcnames 0 : { *(.debug_funcnames) }
    .debug_typenames 0 : { *(.debug_typenames) }
    .debug_varnames  0 : { *(.debug_varnames) }
}
