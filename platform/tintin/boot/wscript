import os
import sys
import waflib.Logs

sys.path.append(os.path.abspath('waftools'))
import objcopy
import gitinfo


def options(opt):
    opt.load('compiler_c')
    opt.add_option('--board', action='store', default='bb2',
                   help='Which board to build for '
                   '(bb2|ev2_4|v1_5|v2_0)',
                   choices=['bb2',
                            'ev2_4',
                            'v1_5',
                            'v2_0'])
    opt.add_option('--nowatchdog', action='store_true',
                   help='Do not enable watchdog timer or reset upon failure')
    opt.add_option('--loglevel', type='int', default=0,
                   help='Set the logging verbosity [default: %default]')
    opt.add_option('--displaydemo', action='store_true',
                   help='Make the bootloader infinitely loop through boot splash and error codes')


def configure(conf):
    CPU_FLAGS = ['-mcpu=cortex-m3', '-mthumb']
    OPT_FLAGS = ['-Os', '-g', '-flto', '-fno-fat-lto-objects', '-ffunction-sections',
                 '-fdata-sections', '-fno-if-conversion', '-fno-optimize-sibling-calls']
    C_FLAGS = [
        '-std=c11', '-ffreestanding',
        '-Wall', '-Wextra', '-Werror', '-Wpointer-arith',
        '-Wno-unused-parameter', '-Wno-missing-field-initializers',
        '-Wno-error=unused-function', '-Wno-error=unused-variable',
        '-Wno-error=unused-parameter', '-Wno-error=unused-but-set-variable',
        '-Wno-packed-bitfield-compat'
        ]

    conf.find_program('arm-none-eabi-gcc', var='CC', mandatory=True)
    conf.env.AS = conf.env.CC
    for tool in 'ar objcopy'.split():
        conf.find_program('arm-none-eabi-' + tool, var=tool.upper(),
                          mandatory=True)
    conf.env.BOARD = conf.options.board
    conf.env.append_value('CFLAGS', CPU_FLAGS + OPT_FLAGS + C_FLAGS)
    conf.env.append_value('DEFINES', [
        '_REENT_SMALL=1',
        'USE_STDPERIPH_DRIVER=1',
        'BOARD_' + conf.options.board.upper()
        ])
    conf.env.append_value('LINKFLAGS', ['-Wl,--cref', '-Wl,--gc-sections',
                          '-Wl,--sort-section=alignment', '-nostdlib'] +
                          CPU_FLAGS + OPT_FLAGS)

    if conf.options.nowatchdog:
        conf.env.append_value('DEFINES', 'NO_WATCHDOG')

    if conf.options.displaydemo:
        conf.env.append_value('DEFINES', 'DISPLAY_DEMO_LOOP')

    conf.env.BOOTLOADER_LENGTH = '16384'
    conf.env.append_value(
            'DEFINES', 'BOOTLOADER_LENGTH=' + conf.env.BOOTLOADER_LENGTH)

    conf.load('gcc gas objcopy ldscript')
    conf.load('binary_header')
    conf.load('file_name_c_define')


def build(bld):
    if bld.cmd == 'install':
        raise Exception("install isn't a supported command. Did you mean flash?")

    linkflags = ['-Wl,-Map,tintin_boot.map']

    sources = (
            bld.path.ant_glob('src/*.S') +
            bld.path.ant_glob('src/**/*.c'))

    bld(features='subst',
        source='src/git_version.auto.h.in',
        target='src/git_version.auto.h',
        **gitinfo.get_git_revision(bld))

    bld.recurse('libc')
    bld.recurse('vendor')
    bld(features='subst',
        source='src/stm32f_flash_boot.ld.in',
        target='src/stm32f_flash_boot.ld')
    bld(features='c asm cprogram objcopy',
        source=sources,
        includes='src',
        target='tintin_boot.elf',
        ldscript='src/stm32f_flash_boot.ld',
        linkflags=linkflags,
        objcopy_bfdname='ihex',
        objcopy_target='tintin_boot.hex',
        use=['stm32_stdlib', 'pblibc'],
        lib=['gcc'])
    bld(rule=objcopy.objcopy_simple_bin, source='tintin_boot.elf', target='tintin_boot.bin')
# vim:filetype=python
