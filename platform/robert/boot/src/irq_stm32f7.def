IRQ_DEF(0, WWDG)                   // Window WatchDog
IRQ_DEF(1, PVD)                    // PVD through EXTI Line detection
IRQ_DEF(2, TAMP_STAMP)             // Tamper and TimeStamps through the EXTI line
IRQ_DEF(3, RTC_WKUP)               // RTC Wakeup through the EXTI line
IRQ_DEF(4, FLASH)                  // FLASH
IRQ_DEF(5, RCC)                    // RCC
IRQ_DEF(6, EXTI0)                  // EXTI Line0
IRQ_DEF(7, EXTI1)                  // EXTI Line1
IRQ_DEF(8, EXTI2)                  // EXTI Line2
IRQ_DEF(9, EXTI3)                  // EXTI Line3
IRQ_DEF(10, EXTI4)                 // EXTI Line4
IRQ_DEF(11, DMA1_Stream0)          // DMA1 Stream 0
IRQ_DEF(12, DMA1_Stream1)          // DMA1 Stream 1
IRQ_DEF(13, DMA1_Stream2)          // DMA1 Stream 2
IRQ_DEF(14, DMA1_Stream3)          // DMA1 Stream 3
IRQ_DEF(15, DMA1_Stream4)          // DMA1 Stream 4
IRQ_DEF(16, DMA1_Stream5)          // DMA1 Stream 5
IRQ_DEF(17, DMA1_Stream6)          // DMA1 Stream 6
IRQ_DEF(18, ADC)                   // ADC1, ADC2 and ADC3s
IRQ_DEF(19, CAN1_TX)               // CAN1 TX
IRQ_DEF(20, CAN1_RX0)              // CAN1 RX0
IRQ_DEF(21, CAN1_RX1)              // CAN1 RX1
IRQ_DEF(22, CAN1_SCE)              // CAN1 SCE
IRQ_DEF(23, EXTI9_5)               // External Line[9:5]s
IRQ_DEF(24, TIM1_BRK_TIM9)         // TIM1 Break and TIM9
IRQ_DEF(25, TIM1_UP_TIM10)         // TIM1 Update and TIM10
IRQ_DEF(26, TIM1_TRG_COM_TIM11)    // TIM1 Trigger and Commutation and TIM11
IRQ_DEF(27, TIM1_CC)               // TIM1 Capture Compare
IRQ_DEF(28, TIM2)                  // TIM2
IRQ_DEF(29, TIM3)                  // TIM3
IRQ_DEF(30, TIM4)                  // TIM4
IRQ_DEF(31, I2C1_EV)               // I2C1 Event
IRQ_DEF(32, I2C1_ER)               // I2C1 Error
IRQ_DEF(33, I2C2_EV)               // I2C2 Event
IRQ_DEF(34, I2C2_ER)               // I2C2 Error
IRQ_DEF(35, SPI1)                  // SPI1
IRQ_DEF(36, SPI2)                  // SPI2
IRQ_DEF(37, USART1)                // USART1
IRQ_DEF(38, USART2)                // USART2
IRQ_DEF(39, USART3)                // USART3
IRQ_DEF(40, EXTI15_10)             // External Line[15:10]s
IRQ_DEF(41, RTC_Alarm)             // RTC Alarm (A and B) through EXTI Line
IRQ_DEF(42, OTG_FS_WKUP)           // USB OTG FS Wakeup through EXTI line
IRQ_DEF(43, TIM8_BRK_TIM12)        // TIM8 Break and TIM12
IRQ_DEF(44, TIM8_UP_TIM13)         // TIM8 Update and TIM13
IRQ_DEF(45, TIM8_TRG_COM_TIM14)    // TIM8 Trigger and Commutation and TIM14
IRQ_DEF(46, TIM8_CC)               // TIM8 Capture Compare
IRQ_DEF(47, DMA1_Stream7)          // DMA1 Stream7
IRQ_DEF(48, FMC)                   // FMC
IRQ_DEF(49, SDMMC1)                // SDMMC1
IRQ_DEF(50, TIM5)                  // TIM5
IRQ_DEF(51, SPI3)                  // SPI3
IRQ_DEF(52, UART4)                 // UART4
IRQ_DEF(53, UART5)                 // UART5
IRQ_DEF(54, TIM6_DAC)              // TIM6 and DAC1&2 underrun errors
IRQ_DEF(55, TIM7)                  // TIM7
IRQ_DEF(56, DMA2_Stream0)          // DMA2 Stream 0
IRQ_DEF(57, DMA2_Stream1)          // DMA2 Stream 1
IRQ_DEF(58, DMA2_Stream2)          // DMA2 Stream 2
IRQ_DEF(59, DMA2_Stream3)          // DMA2 Stream 3
IRQ_DEF(60, DMA2_Stream4)          // DMA2 Stream 4
IRQ_DEF(61, ETH)                   // Ethernet
IRQ_DEF(62, ETH_WKUP)              // Ethernet Wakeup through EXTI line
IRQ_DEF(63, CAN2_TX)               // CAN2 TX
IRQ_DEF(64, CAN2_RX0)              // CAN2 RX0
IRQ_DEF(65, CAN2_RX1)              // CAN2 RX1
IRQ_DEF(66, CAN2_SCE)              // CAN2 SCE
IRQ_DEF(67, OTG_FS)                // USB OTG FS
IRQ_DEF(68, DMA2_Stream5)          // DMA2 Stream 5
IRQ_DEF(69, DMA2_Stream6)          // DMA2 Stream 6
IRQ_DEF(70, DMA2_Stream7)          // DMA2 Stream 7
IRQ_DEF(71, USART6)                // USART6
IRQ_DEF(72, I2C3_EV)               // I2C3 event
IRQ_DEF(73, I2C3_ER)               // I2C3 error
IRQ_DEF(74, OTG_HS_EP1_OUT)        // USB OTG HS End Point 1 Out
IRQ_DEF(75, OTG_HS_EP1_IN)         // USB OTG HS End Point 1 In
IRQ_DEF(76, OTG_HS_WKUP)           // USB OTG HS Wakeup through EXTI
IRQ_DEF(77, OTG_HS)                // USB OTG HS
IRQ_DEF(78, DCMI)                  // DCMI
IRQ_DEF(79, CRYP)                  // CRYP crypto
IRQ_DEF(80, HASH_RNG)              // Hash and Rng
IRQ_DEF(81, FPU)                   // FPU
IRQ_DEF(82, UART7)                 // UART7
IRQ_DEF(83, UART8)                 // UART8
IRQ_DEF(84, SPI4)                  // SPI4
IRQ_DEF(85, SPI5)                  // SPI5
IRQ_DEF(86, SPI6)                  // SPI6
IRQ_DEF(87, SAI1)                  // SAI1
IRQ_DEF(88, LTDC)                  // LTDC
IRQ_DEF(89, LTDC_ER)               // LTDC_ER
IRQ_DEF(90, DMA2D)                 // DMA2D
IRQ_DEF(91, SAI2)                  // SAI2
IRQ_DEF(92, QUADSPI)               // Quad SPI
IRQ_DEF(93, LPTIM1)                // LP TIM1
IRQ_DEF(94, CEC)                   // HDMI-CEC
IRQ_DEF(95, I2C4_EV)               // I2C4 Event
IRQ_DEF(96, I2C4_ER)               // I2C4 Error
IRQ_DEF(97, SPDIF_RX)              // SPDIF-RX
IRQ_DEF(99, DFSDM0)                // DFSDM Filter1
IRQ_DEF(100, DFSDM1)               // DFSDM Filter2
IRQ_DEF(101, DFSDM2)               // DFSDM Filter3
IRQ_DEF(102, DFSDM3)               // DFSDM Filter4
IRQ_DEF(103, SDMMC2)               // SDMMC2
IRQ_DEF(104, CAN3_TX)              // CAN3 TX
IRQ_DEF(105, CAN3_RX0)             // CAN3 RX0
IRQ_DEF(106, CAN3_RX1)             // CAN3 RX1
IRQ_DEF(107, CAN3_SCE)             // CAN3 SCE
IRQ_DEF(108, JPEG)                 // JPEG
IRQ_DEF(109, MDIOS)                // MDIO Slave
