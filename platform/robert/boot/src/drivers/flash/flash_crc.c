/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "drivers/flash.h"

#include "util/crc32.h"

#define CRC_CHUNK_SIZE 1024

uint32_t flash_calculate_checksum(uint32_t flash_addr, uint32_t num_bytes) {
  uint8_t buffer[CRC_CHUNK_SIZE];

  uint32_t crc = CRC32_INIT;

  while (num_bytes > CRC_CHUNK_SIZE) {
    flash_read_bytes(buffer, flash_addr, CRC_CHUNK_SIZE);
    crc = crc32(crc, buffer, CRC_CHUNK_SIZE);

    num_bytes -= CRC_CHUNK_SIZE;
    flash_addr += CRC_CHUNK_SIZE;
  }

  flash_read_bytes(buffer, flash_addr, num_bytes);
  return crc32(crc, buffer, num_bytes);
}
