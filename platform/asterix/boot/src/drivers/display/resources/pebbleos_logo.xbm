#define pebbleos_logo_width 112
#define pebbleos_logo_height 29
static const unsigned char pebbleos_logo_bits[] = {
   0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04,
   0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
   0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
   0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
   0x00, 0x00, 0x00, 0x80, 0x00, 0x04, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00,
   0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x0e, 0x10, 0x00, 0x00, 0x00,
   0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00,
   0x00, 0x00, 0x00, 0xc0, 0x00, 0x60, 0x00, 0x30, 0x00, 0x00, 0xfc, 0xf1,
   0x07, 0x3e, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x60, 0x00, 0x30, 0x00, 0x00,
   0xfc, 0xe0, 0x07, 0x7f, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x60, 0x00, 0x30,
   0x00, 0x00, 0x0c, 0x00, 0x86, 0x63, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x60,
   0x00, 0x30, 0x00, 0x00, 0x0c, 0x00, 0x86, 0x01, 0xf0, 0x01, 0xf8, 0xc0,
   0x7c, 0x60, 0x3e, 0x30, 0xe0, 0x03, 0x0c, 0x00, 0x86, 0x01, 0xfc, 0x07,
   0xfe, 0xc3, 0xff, 0xe1, 0xff, 0x30, 0xf8, 0x0f, 0x4e, 0x10, 0x8e, 0x03,
   0x0e, 0x0e, 0x07, 0xc7, 0x83, 0xe3, 0xc1, 0x31, 0x1c, 0x1c, 0x46, 0x10,
   0x0c, 0x0f, 0x06, 0x0c, 0x03, 0xc6, 0x01, 0xe3, 0x80, 0x31, 0x0c, 0x18,
   0x43, 0x10, 0x18, 0x3e, 0x03, 0x98, 0x81, 0xcf, 0x00, 0x66, 0x00, 0x33,
   0x06, 0xbe, 0x03, 0x00, 0x38, 0x78, 0x03, 0x98, 0xf9, 0xcf, 0x00, 0x66,
   0x00, 0x33, 0xe6, 0x3f, 0x43, 0x10, 0x18, 0x60, 0x03, 0x98, 0xff, 0xc0,
   0x00, 0x66, 0x00, 0x33, 0xfe, 0x03, 0x86, 0x0f, 0x0c, 0xc0, 0x03, 0x98,
   0x0f, 0xc0, 0x00, 0x66, 0x00, 0x33, 0x3e, 0x00, 0x0e, 0x00, 0x0e, 0xc0,
   0x03, 0x98, 0x01, 0xcc, 0x00, 0x66, 0x00, 0x33, 0x06, 0x30, 0x0c, 0x00,
   0xc6, 0xc0, 0x07, 0x0c, 0x03, 0x86, 0x01, 0xc3, 0x80, 0x31, 0x0c, 0x18,
   0x0c, 0x00, 0xc6, 0xc0, 0x0f, 0x0e, 0x8f, 0x87, 0x83, 0xc3, 0xc1, 0x31,
   0x1c, 0x1c, 0x0c, 0x00, 0x86, 0x61, 0xff, 0x07, 0xfe, 0x03, 0xff, 0x81,
   0xff, 0x70, 0xf8, 0x0f, 0xfc, 0xe0, 0x87, 0x7f, 0xf3, 0x01, 0xf8, 0x00,
   0x7c, 0x00, 0x3e, 0x60, 0xe0, 0x03, 0xfc, 0xf1, 0x07, 0x1e, 0x03, 0x00,
   0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00,
   0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x0e,
   0x10, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80,
   0x00, 0x04, 0x20, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
   0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
   0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
   0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00 };
