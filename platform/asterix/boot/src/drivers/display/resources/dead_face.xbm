#define dead_face_width 69
#define dead_face_height 71
static const unsigned char dead_face_bits[] = {
   0x00, 0x00, 0x00, 0xe0, 0xff, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
   0xf8, 0xff, 0x7f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xff, 0xff,
   0x03, 0x00, 0x00, 0x00, 0x00, 0x80, 0xff, 0xff, 0xff, 0x1f, 0x00, 0x00,
   0x00, 0x00, 0xe0, 0x1f, 0x00, 0xf8, 0x7f, 0x00, 0x00, 0x00, 0x00, 0xf8,
   0x0f, 0x00, 0xc0, 0xff, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x03, 0x00, 0x00,
   0xfe, 0x01, 0x00, 0x00, 0x80, 0x7f, 0x00, 0x00, 0x00, 0xf0, 0x03, 0x00,
   0x00, 0xc0, 0x1f, 0x00, 0x00, 0x00, 0xc0, 0x07, 0x00, 0x00, 0xc0, 0x07,
   0x00, 0x00, 0x00, 0x80, 0x0f, 0x00, 0x00, 0xe0, 0x03, 0x00, 0x00, 0x00,
   0x00, 0x1f, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x3e, 0x00,
   0x00, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7c, 0x00, 0x00, 0xf0, 0x00,
   0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00,
   0x00, 0xf0, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x00,
   0x00, 0x3c, 0x30, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x3c, 0x70,
   0x00, 0x00, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x1e, 0x70, 0x00, 0x70, 0x00,
   0x00, 0xc0, 0x03, 0x00, 0x1e, 0xf0, 0x00, 0x78, 0x00, 0x00, 0xc0, 0x03,
   0x00, 0x1e, 0xe0, 0x00, 0x7c, 0x00, 0x00, 0x80, 0x07, 0x00, 0x0f, 0xe0,
   0x01, 0x3e, 0x00, 0x00, 0x80, 0x07, 0x00, 0x0f, 0xc0, 0x01, 0x1f, 0x00,
   0x00, 0x80, 0x07, 0x00, 0x0f, 0xc0, 0x83, 0x0f, 0x00, 0x00, 0x80, 0x07,
   0x00, 0x0f, 0xc0, 0xc3, 0x07, 0x00, 0x00, 0x80, 0x07, 0x80, 0x07, 0xe0,
   0xc3, 0x03, 0x00, 0x00, 0x80, 0x07, 0x80, 0x07, 0xf0, 0xc1, 0x03, 0x00,
   0x00, 0x80, 0x07, 0x80, 0x07, 0xf8, 0x80, 0x03, 0x00, 0x00, 0x80, 0x07,
   0x80, 0x07, 0x7c, 0x80, 0x07, 0x00, 0x00, 0x80, 0x07, 0xc0, 0x03, 0x3e,
   0x00, 0x07, 0x00, 0x00, 0x80, 0x07, 0xc0, 0x03, 0x1e, 0x00, 0x0f, 0x00,
   0x00, 0x80, 0x07, 0xe0, 0x03, 0x0e, 0x00, 0x0e, 0x00, 0x00, 0x80, 0x07,
   0xf0, 0x01, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x80, 0x07, 0xf8, 0x00, 0x00,
   0x00, 0x0c, 0x00, 0x00, 0x80, 0x07, 0x78, 0x00, 0xc0, 0x71, 0x00, 0x00,
   0x00, 0x80, 0x07, 0x7c, 0x00, 0xe0, 0x79, 0x00, 0x18, 0x00, 0x80, 0x07,
   0x3c, 0x00, 0xf0, 0xff, 0x00, 0x3c, 0x00, 0x80, 0x07, 0x3c, 0x00, 0xf8,
   0xff, 0x00, 0x3c, 0x00, 0x80, 0x07, 0x3e, 0x00, 0xf8, 0xff, 0x01, 0x1e,
   0x00, 0x80, 0x07, 0x1e, 0x00, 0x38, 0xcf, 0x01, 0x1e, 0x00, 0x80, 0x07,
   0x1e, 0x00, 0x00, 0xc7, 0x01, 0x0f, 0x00, 0xc0, 0x03, 0x1f, 0x00, 0x00,
   0x00, 0x00, 0x0f, 0x00, 0xc0, 0x03, 0x0f, 0x00, 0x00, 0x00, 0x80, 0x07,
   0x00, 0xe0, 0x01, 0x0f, 0x00, 0x00, 0x00, 0x80, 0x07, 0x00, 0xe0, 0x01,
   0x0f, 0x00, 0x00, 0x00, 0xc0, 0x03, 0x00, 0xf0, 0x00, 0x0f, 0x00, 0x00,
   0x00, 0xc0, 0x03, 0x00, 0xf0, 0x00, 0x0f, 0x00, 0x00, 0x00, 0xc0, 0x03,
   0x00, 0x78, 0x00, 0x0f, 0x60, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x78, 0x00,
   0x0f, 0xf8, 0x00, 0x00, 0xc0, 0x03, 0x3c, 0x3c, 0x00, 0x0f, 0xfe, 0x01,
   0x00, 0xc0, 0x03, 0x3f, 0x3c, 0x00, 0x9f, 0xff, 0x03, 0x00, 0xc0, 0xc3,
   0x3f, 0x1e, 0x00, 0xff, 0xff, 0x07, 0x00, 0xc0, 0xe3, 0x0f, 0x1e, 0x00,
   0xfe, 0x87, 0x0f, 0x00, 0xc0, 0xff, 0x03, 0x1e, 0x00, 0xf8, 0x03, 0x1f,
   0x00, 0xc0, 0xff, 0x00, 0x1e, 0x00, 0x60, 0x00, 0x3e, 0x00, 0x80, 0x7f,
   0x00, 0x1e, 0x00, 0x00, 0x00, 0xfc, 0x00, 0x00, 0x3e, 0x00, 0x1e, 0x00,
   0x00, 0x00, 0xf8, 0x0f, 0x00, 0x18, 0x00, 0x1e, 0x00, 0x00, 0x00, 0xf0,
   0x7f, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0xe0, 0xff, 0x07, 0x00,
   0x00, 0x1e, 0x00, 0x00, 0x00, 0x80, 0xff, 0x3f, 0x00, 0x00, 0x1e, 0x00,
   0x00, 0x00, 0x00, 0xf8, 0xff, 0x03, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x00,
   0xc0, 0xff, 0x0f, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0x3f,
   0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0xff, 0x00, 0xf8, 0x00,
   0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x03, 0xf0, 0x01, 0x00, 0x00, 0x00,
   0x00, 0x00, 0xf0, 0x1f, 0xe0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0,
   0x7f, 0xc0, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0f,
   0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0xff, 0x1f, 0x00, 0x00, 0x00,
   0x00, 0x00, 0x00, 0xf0, 0xff, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
   0xc0, 0xff, 0x0f };
