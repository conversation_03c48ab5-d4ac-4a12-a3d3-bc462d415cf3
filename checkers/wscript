from os import path

def options(opt):
    opt.load('compiler_cxx')
    opt.add_option('--checker', action='store', default='all')

def configure(conf):
    conf.env.CXX = 'clang++'
    conf.load('compiler_cxx')

    conf.env.append_value('DEFINES', ['__STDC_CONSTANT_MACROS',
                                      '__STDC_LIMIT_MACROS'])

    conf.check_cfg(msg='Checking for llvm config',
                   path='llvm-config',
                   package='',
                   args='--cxxflags --ldflags --libs --system-libs',
                   uselib_store='LLVM')

    clang_libs = ['clang',
                  'clangARCMigrate',
                  'clangAST',
                  'clangASTMatchers',
                  'clangAnalysis',
                  'clangApplyReplacements',
                  'clangBasic',
                  'clangCodeGen',
                  'clangDriver',
                  'clangDynamicASTMatchers',
                  'clangEdit',
                  'clangFormat',
                  'clangFrontend',
                  'clangFrontendTool',
                  'clangIndex',
                  'clangLex',
                  'clangParse',
                  'clangQuery',
                  'clangRename',
                  'clangRewrite',
                  'clangRewriteFrontend',
                  'clangSema',
                  'clangSerialization',
                  'clangStaticAnalyzerCheckers',
                  'clangStaticAnalyzerCore',
                  'clangStaticAnalyzerFrontend',
                  'clangTooling',
                  'clangToolingCore']

    conf.check_cxx(msg='Checking for clang++',
                   uselib_store='CLANG',
                   use=['LLVM'],
                   lib=clang_libs)

def build(bld):
    checkers = []

    if bld.options.checker == 'all':
        checkers = bld.path.ant_glob('*.cpp')
    else:
        checkers = [ bld.path.make_node(bld.options.checker) ]

    for checker in checkers:
        source = [ checker ]
        target = checker.change_ext('.dylib')
        bld.shlib(source=source,
                  target=target,
                  use=['CLANG', 'LLVM'],
                  cppflags=['-fno-rtti', '-std=c++11', '-fPIC'])

# vim:filetype=python
