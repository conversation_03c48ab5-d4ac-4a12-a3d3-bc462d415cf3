<svg width="128" height="134" viewBox="0 0 128 134" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="27" width="70" height="132" rx="1" fill="#236BB3"/>
<path d="M0 60C0 58.3431 1.34315 57 3 57H8V77H3C1.34315 77 0 75.6569 0 74V60Z" fill="#3B8FE3"/>
<path d="M116 57H121C122.657 57 124 58.3431 124 60V74C124 75.6569 122.657 77 121 77H116V57Z" fill="#3B8FE3"/>
<path d="M115 84L119.33 86.5C120.765 87.3284 121.257 89.1632 120.428 90.5981L113.428 102.722C112.6 104.157 110.765 104.649 109.33 103.821L105 101.321L115 84Z" fill="#3B8FE3"/>
<path d="M105 33L109.33 30.5C110.765 29.6716 112.6 30.1632 113.428 31.5981L120.428 43.7224C121.257 45.1573 120.765 46.9921 119.33 47.8205L115 50.3205L105 33Z" fill="#3B8FE3"/>
<g filter="url(#filter0_d_25_31)">
<circle cx="62" cy="66" r="56" fill="#55AAFF"/>
</g>
<path d="M40 91H84L85 84L92 74L85 64L83 53L72 51L62 44L52 51L41 53L39 64L32 74L39 84L40 91Z" fill="white" stroke="black" stroke-width="4" stroke-miterlimit="10" stroke-linecap="square" stroke-linejoin="round"/>
<path d="M48 65V72" stroke="black" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M69 65V72" stroke="black" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M48 81L58 84L69 81" stroke="black" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M29 91H94" stroke="black" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M26 74H24" stroke="black" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M100 74H98" stroke="black" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M62 38V32" stroke="black" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M88 48L91 45" stroke="black" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M36 48L33 45" stroke="black" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<mask id="mask0_25_31" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="124" height="132">
<path d="M96 0C96.5523 0 97 0.447715 97 1V22.2832C100.576 25.1495 103.791 28.4457 106.57 32.0928L109.33 30.5C110.765 29.6717 112.599 30.1632 113.428 31.5977L120.428 43.7227C121.256 45.1575 120.765 46.9919 119.33 47.8203L115.661 49.9375C116.347 52.2333 116.891 54.5908 117.28 57H121C122.657 57 124 58.3431 124 60V74C124 75.6569 122.657 77 121 77H116.918C116.44 79.3991 115.809 81.7426 115.035 84.0205L119.33 86.5C120.765 87.3284 121.256 89.1628 120.428 90.5977L113.428 102.723C112.599 104.157 110.765 104.649 109.33 103.82L105.31 101.499C102.833 104.517 100.047 107.273 97 109.716V131C97 131.552 96.5523 132 96 132H28C27.4477 132 27 131.552 27 131V109.716C16.9237 101.638 9.70144 90.149 7.08203 77H3C1.34315 77 6.44266e-08 75.6569 0 74V60C0 58.3431 1.34315 57 3 57H6.71973C8.97889 43.0169 16.4156 30.7681 27 22.2832V1C27 0.447715 27.4477 3.01994e-09 28 0H96Z" fill="white"/>
</mask>
<g mask="url(#mask0_25_31)">
<rect width="124" height="132" fill="url(#paint0_radial_25_31)"/>
</g>
<defs>
<filter id="filter0_d_25_31" x="0" y="6" width="128" height="128" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.115705 0 0 0 0 0.365385 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_25_31"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_25_31" result="shape"/>
</filter>
<radialGradient id="paint0_radial_25_31" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(41 37.5) rotate(75.3168) scale(96.6566 90.7986)">
<stop stop-color="white" stop-opacity="0.14"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
</defs>
</svg>
