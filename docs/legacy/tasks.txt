FreeRTOS lets us create tasks, which are independant threads of execution. We create a few ourselves, and more are created internally by FreeRTOS. We have pre-emption enabled in our FreeRTOS configuration, so we must be mindful of which other tasks are mucking with the same data. This document describes the tasks that exist in our system.

FreeRTOS Tasks
==============
Tasks defined internally by FreeRTOS.

"Tmr Svc" - The timer task
--------------------------
Timers that are registered using our timer infrastructure are executed on this task. This is a FreeRTOS subsystem that we've wrapped with the goodies in fw/timers.h.

"IDLE" - The idle task
----------------------
This is a special task used by the FreeRTOS scheduler. It's defined at it's own priority level which is at the lowest priority. If no other task is ready to run, either due to waiting on a semaphore or waiting using vTaskDelay (or something like that), the idle task is chosen to run instead.

We have modified FreeRTOS such that if we're in the idle task, we enter a lower power mode, either sleep or stop. Stop mode is special in that peripheral clocks are shut down when we go into stop and are not automatically turned back on when we leave stop mode. This means we have to go through and turn them all back on. This is what the `register_stop_mode_resume_callback` function does. It allows individual drivers to register callbacks that are called when we leave stop from the idle thread. This comes with a caveat though. Since the idle thread only ever runs when there's nothing else to run, the scheduler assumes that there is always a task to run. This means that if the idle task is stopped or delayed for any reason, the scheduler will explode. Therefore you are not permitted to do operations that may stop the task's execution from within the resume callback.

Pebble Tasks
============

"main" - The main task
----------------------
The first task in our system. Runs the all the driver initialization code, and then gets taken over by the main launcher loop. The launcher event queue is then serviced forever in a loop.

"system_task" - The system task
-------------------------------
A lower priority task that services callbacks from a worker queue. If you are doing something that may take more than a few milliseconds, it makes sense to delegate it to this queue. For example, flash writes and reads during firmware updates are done on this task.

"app" - The application task
----------------------------
This task is created for the currently running app. No task is created for the launcher "app", as it's not a real app and just uses the main task. This task's lifetime is limited to the lifetime of the app.

???

Open Questions
==============

Which tasks are allowed to manipulate the window state? What if the launcher wants to push a notification window at the same time as an app pushes it's own window?
