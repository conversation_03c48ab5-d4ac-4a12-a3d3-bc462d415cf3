SPI Flash Imaging Using The Serial Console
==========================================

NOTE: This document describes the original method of flash imaging
over the serial console. It is no longer used and has been superseded by
newer, faster methods.

By default the serial console is in logging mode, where log messages
are echoed to the screen as they are issued. To enter the serial
prompt, send a CTRL-C character to the serial console. The prompt
looks like a single right angle bracket ('>'). Commands may be typed
at this prompt and executed using the enter key.

There are several commands available that can be used to manipulate 
the SPI flash contents.

dump flash <address> <length>
=============================

This command will dump a section of SPI flash. The address must be specified
in hex with a leading '0x'. The length is in bytes.

Ex:

>dump flash 0x200000 128
[00:26:38.263] ../src/core/console/prompt.c:115> Data at address 0x200000
[00:26:38.271] ../src/core/util/hexdump.c:11> 0000  0c 00 00 00 74 bf 03 00  32 9a 8a 4b 90 81 00 20   ....t... 2..K...
[00:26:38.283] ../src/core/util/hexdump.c:11> 0010  2d a0 01 08 7d a0 01 08  19 93 01 08 7d a0 01 08   -...}... ....}...
[00:26:38.294] ../src/core/util/hexdump.c:11> 0020  7d a0 01 08 7d a0 01 08  00 00 00 00 00 00 00 00   }...}... ........
[00:26:38.307] ../src/core/util/hexdump.c:11> 0030  00 00 00 00 00 00 00 00  01 8a 01 08 7d a0 01 08   ........ ....}...
[00:26:38.319] ../src/core/util/hexdump.c:11> 0040  00 00 00 00 9d 8a 01 08  d9 8a 01 08 7d a0 01 08   ........ ....}...
[00:26:38.332] ../src/core/util/hexdump.c:11> 0050  7d a0 01 08 7d a0 01 08  7d c9 01 08 7d a0 01 08   }...}... }...}...
[00:26:38.343] ../src/core/util/hexdump.c:11> 0060  7d a0 01 08 29 59 01 08  3b 59 01 08 4d 59 01 08   }...)Y.. ;Y..MY..
[00:26:38.355] ../src/core/util/hexdump.c:11> 0070  5f 59 01 08 7d a0 01 08  7d a0 01 08 7d a0 01 08   _Y..}... }...}...
>

erase flash <address> <length>
==============================

This command will erase a section of SPI flash in 64k chunks. The address must be
specified in hex with a leading '0x'. The address must be 64k aligned. The length
is in bytes, and will be rounded up to the next 64k boundary. 'OK' will be echoed to
the console once the erase operation is complete.

Ex:

>erase flash 0x0 128
Erasing 64k flash sectors starting at address 0x0 for 128 bytes (rounded up to 65536 bytes)
OK
>

write flash <address>
=====================

This command allows a user to write bytes to SPI flash. Please ensure
the region being written to has already been erased. The address must
be specified in hex with a leading '0x'.

Bytes must be written using base64 encoding. The base64 characters are
not echoed back to the console as they are written. Bytes are written
to flash in chunks of 1024 base64 characters, corresponding to 768
decoded bytes. The watch will echo a '#' character to acknowledge
receipt of a chunk, followed by echoing 'OK' when the watch is ready
to receive the next chunk. The end of the final chunk is indicated by
sending a CTRL-D character (EOF). The current chunk will be flushed to
SPI flash upon receiving an EOF character. Remember to pad the final
chunk with '=' characters if it's not a multiple of 3 bytes, as per
the base64 encoding standard. To exit the 'write flash' mode without
flushing the current chunk, enter a CTRL-C character.

If a chunk is malformed due to invalid characters or incorrect
padding, 'Decoding Error' will be printed to the serial port and the
normal serial prompt will return. Note that any previous chunks that
were well formed have already been written to SPI flash.

Ex:

>write flash 0x0 
Flash imaging mode, CTRL-D to stop
#OK
>

(NB: Your base64 data is not echoed back when writing to flash. The #OK will show up
when a chunk is complete or CTRL-D is used.)

