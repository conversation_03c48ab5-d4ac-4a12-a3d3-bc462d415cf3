digraph G {
  compound=true;
  Initialized [label=<<b>Initialized (0)</b><br/><font color='#6E6E6E'>q is unknown</font>>];
  PreStandby [label=<<b>PreStandby (8)</b><br/><font color='#6E6E6E'>q &lt; 2 mAh</font>>];
  Standby [label=<<b>Standby (9)</b><br/><font color='#6E6E6E'>q &lt; 2 mAh (unless manually entered)</font>>];

  /* Charging flow */
  subgraph cluster0 {
    {rank=same;
      FullPlugged [label=<<b>FullPlugged (4)</b><br/><font color='#6E6E6E'>q ~= 130mAh</font>>, group="charging"];
      O1[style=invis];
    }
    {rank=same;
      HighCharging [label=<<b>HighCharging (5)</b><br/><font color='#6E6E6E'>25 mAh &lt;= q &lt; 130 mAh</font>>, group="charging"];
      O2[style=invis];
    }
    {rank=same;
      LowCharging [label=<<b>LowCharging (6)</b><br/><font color='#6E6E6E'>5 mAh &lt;= q &lt; 25 mAh</font>>, group="charging"];
      O3[style=invis];
    }
    {rank=same;
      CriticalCharging [label=<<b>CriticalCharging (7)</b><br/><font color='#6E6E6E'>q &lt; 5 mAh</font>>, group="charging"];
      O4[style=invis];
    }

    CriticalCharging -> LowCharging [label=">3.2V", constraint=false, weight=100]; 
    LowCharging -> HighCharging [label=">3.8V", constraint=false, weight=100];
    HighCharging -> FullPlugged [color=green, constraint=false, weight=100];

    edge[style=invis];
    O1->O2->O3->O4;

    color=blue;
  }

  subgraph cluster1 {
    FullDischarging [label=<<b>FullDischarging (1)</b><br/><font color='#6E6E6E'>q &gt; 30mAh (~125h)</font>>, group="discharging"];
    LowDischarging [label=<<b>LowDischarging (2)</b><br/><font color='#6E6E6E'>5 mAh &lt;= q &lt; 30mAh (~31h)</font>>, group="discharging"];
    CriticalDischarging [label=<<b>CriticalDischarging (3)</b><br/><font color='#6E6E6E'>2 mAh &lt;= q &lt; 5 mAh (~3h)</font>>, group="discharging"];

    FullDischarging -> LowDischarging [label="<=3.7V"];
    LowDischarging -> CriticalDischarging [label="<=3.3V"];
    CriticalDischarging -> PreStandby [label="<=3.1V"];

    color=red;
  }

  Initialized -> FullDischarging [color=red, lhead=cluster1];
  Initialized -> FullPlugged [color=blue, lhead=cluster0, constraint=false];


  FullPlugged -> FullDischarging  [color=red]
  CriticalCharging -> PreStandby  [color=red, weight=0.1];
  HighCharging -> FullDischarging [color=red, constraint=false, weight=0.1];
  LowCharging -> FullDischarging  [color=red, constraint=false, weight=0.1];

  FullDischarging -> LowCharging     [color=blue, weight=0.01];
  FullDischarging -> HighCharging    [color=blue, constraint=false, weight=0.1];
  LowDischarging -> LowCharging      [color=blue, constraint=false, weight=0.1];
  CriticalDischarging -> LowCharging [color=blue, constraint=false, weight=0.1];


  PreStandby -> CriticalCharging [color=blue, weight=0.1];
  PreStandby -> Standby [label="30s timeout", weight=0.1];
}
