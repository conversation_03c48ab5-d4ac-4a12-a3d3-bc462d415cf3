name: 'Close stale pull requests'

on:
  schedule:
  - cron: '0 0 * * *'

permissions:
  pull-requests: write

jobs:
  stale:
    runs-on: ubuntu-24.04
    steps:
    - uses: actions/stale@v9
      with:
        stale-pr-message: 'This pull request has been marked as stale because it has been open (more than) 30 days with no activity. Remove the stale label or add a comment saying that you would like to have the label removed otherwise this pull request will automatically be closed in 7 days. Note, that you can always re-open a closed pull request at any time.'
        days-before-pr-stale: 30
        days-before-pr-close: 7
        stale-pr-label: 'stale'
