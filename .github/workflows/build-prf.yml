name: Build PRF

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
    paths:
      - '.github/workflows/build-prf.yml'
      - 'resources/**'
      - 'src/**'
      - 'tools/**'
      - 'third_party/**'
      - 'waftools/**'
      - 'waf'
      - 'wscript'

jobs:
  build:
    runs-on: ubuntu-24.04

    container:
      image: ghcr.io/pebble-dev/pebbleos-docker:v1

    strategy:
      matrix:
        board: ["asterix", "obelix", "obelix_bb"]
        mode: ["normal", "mfg"]

    steps:
      - name: Mark Github workspace as safe
        run: git config --system --add safe.directory "${GITHUB_WORKSPACE}"

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: true

      - name: Install Python dependencies
        run: |
          pip install -U pip
          pip install -r requirements.txt

      - name: Configure
        run: |
          if [ "${{ matrix.mode }}" == "mfg" ]; then
            OPTS="--mfg --nohash"
          fi

          ./waf configure --board ${{ matrix.board }} $OPTS

      - name: Build
        run: ./waf build_prf

      - name: Bundle
        run: ./waf bundle_prf

      - name: Store
        uses: actions/upload-artifact@v4
        with:
          name: prf-${{ matrix.board }}-${{ matrix.mode }}
          path: |
            build/**/*.elf
            build/**/*.pbz
            build/prf/src/fw/tintin_fw_loghash_dict.json

      - name: Get Build ID
        id: build_id
        run: |
          echo "BUILD_ID=$(arm-none-eabi-readelf -n build/prf/src/fw/tintin_fw.elf | sed -n -e 's/^.*Build ID: //p')" >> "$GITHUB_OUTPUT"

      - name: Upload log hash dictionary
        uses: Noelware/s3-action@2.3.1
        if: ${{ github.event_name == 'push' }}
        with:
          access-key-id: ${{ secrets.LOG_HASH_BUCKET_KEY_ID }}
          secret-key: ${{ secrets.LOG_HASH_BUCKET_SECRET }}
          endpoint: ${{ vars.LOG_HASH_BUCKET_ENDPOINT }}
          bucket: ${{ vars.LOG_HASH_BUCKET_NAME }}
          files: |
            build/prf/src/fw/tintin_fw_loghash_dict.json
          path-format: ${{ steps.build_id.outputs.BUILD_ID }}-${{ github.sha }}-prf.json
