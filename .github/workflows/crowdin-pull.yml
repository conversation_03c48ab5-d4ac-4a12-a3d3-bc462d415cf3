name: Pull Translations from Crowdin

on:
  schedule:
    - cron: '0 0 * * 0' # Runs weekly at midnight on Sunday
  workflow_dispatch: # Allows manual triggering of the workflow

jobs:
  pull-translations:
    runs-on: ubuntu-24.04
    if: github.repository == 'pebble-dev/pebble-firmware'

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Pull translations from Crowdin
        uses: crowdin/github-action@v2
        with:
          upload_translations: false
          download_translations: true
          download_sources: true
          commit_message: 'resources: update translations from crowdin'
        env:
          CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}
          CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
