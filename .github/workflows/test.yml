name: Test

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
    paths:
      - '.github/workflows/test.yml'
      - 'resources/**'
      - 'sdk/**'
      - 'src/**'
      - 'stored_apps/**'
      - 'tools/**'
      - 'third_party/**'
      - 'waftools/**'
      - 'waf'
      - 'wscript'

env:
  TEST_BOARD: 'snowy_bb2'

jobs:
  build:
    runs-on: ubuntu-24.04
    container:
      image: ghcr.io/pebble-dev/pebbleos-docker:v1
    steps:
      - name: Mark Github workspace as safe
        run: git config --system --add safe.directory "${GITHUB_WORKSPACE}" 

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: true

      - name: Install Python dependencies
        run: |
          pip install -U pip
          pip install -r requirements.txt

      - name: Configure
        run: ./waf configure --board ${{env.TEST_BOARD}}

      - name: Run tests
        run: ./waf test
        continue-on-error: true

      - name: Publish Test Report
        uses: mikepenz/action-junit-report@v5
        if: success() || failure()
        with:
          report_paths: build/test/junit.xml
          annotate_only: true

      - name: Store failed test images
        uses: actions/upload-artifact@v4
        with:
          name: failed_diff_images
          path: build/test/tests/failed/*-diff.png
