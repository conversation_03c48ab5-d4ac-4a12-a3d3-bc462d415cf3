name: Release

on:
  push:
    tags:
      - v*

env:
  MEMFAULT_CLI_VERSION: "1.6.0"

jobs:
  build:
    runs-on: ubuntu-24.04

    container:
      image: ghcr.io/pebble-dev/pebbleos-docker:v1

    strategy:
      matrix:
        board: ["asterix"]

    steps:
      - name: <PERSON> workspace as safe
        run: git config --system --add safe.directory "${GITHUB_WORKSPACE}"

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: true

      - name: Fetch tags
        run: |
          git fetch --tags --force

      - name: Install Python dependencies
        run: |
          pip install -U pip
          pip install -r requirements.txt

      - name: Obtain platform name
        id: get-platform
        run: |
          BOARD=${{ matrix.board }}
          PLATFORM=${BOARD%%_*}
          echo "platform=$PLATFORM" >> "$GITHUB_OUTPUT"

      - name: Configure bootloader
        working-directory: platform/${{ steps.get-platform.outputs.platform }}/boot
        run: ./waf configure --board ${{ matrix.board }}

      - name: Build bootloader
        working-directory: platform/${{ steps.get-platform.outputs.platform }}/boot
        run: ./waf build

      - name: Copy bootloader artifacts
        run: |
          mkdir -p artifacts
          cp platform/${{ steps.get-platform.outputs.platform }}/boot/build/tintin_boot.hex \
             artifacts/bootloader_${{ matrix.board }}_${{github.ref_name}}.hex

      - name: Configure bootloader (nowatchdog)
        working-directory: platform/${{ steps.get-platform.outputs.platform }}/boot
        run: ./waf configure --board ${{ matrix.board }} --nowatchdog

      - name: Build bootloader (nowatchdog)
        working-directory: platform/${{ steps.get-platform.outputs.platform }}/boot
        run: ./waf build

      - name: Copy bootloader artifacts (nowatchdog)
        run: |
          mkdir -p artifacts
          cp platform/${{ steps.get-platform.outputs.platform }}/boot/build/tintin_boot.hex \
             artifacts/bootloader_nowatchdog_${{ matrix.board }}_${{github.ref_name}}.hex

      - name: Configure
        run: ./waf configure --board ${{ matrix.board }} --release

      - name: Build firmware
        run: ./waf build

      - name: Bundle firmware
        run: ./waf bundle

      - name: Copy firmware artifacts
        run: |
          mkdir -p artifacts
          cp build/src/fw/tintin_fw.hex artifacts/firmware_${{ matrix.board }}_${{github.ref_name}}.hex
          cp build/src/fw/tintin_fw.elf artifacts/firmware_${{ matrix.board }}_${{github.ref_name}}.elf
          cp build/*.pbz artifacts
          cp build/src/fw/tintin_fw_loghash_dict.json artifacts/firmware_${{ matrix.board }}_${{github.ref_name}}_loghash_dict.json

      - name: Get Build ID
        id: build_id
        run: |
          echo "BUILD_ID=$(arm-none-eabi-readelf -n build/src/fw/tintin_fw.elf | sed -n -e 's/^.*Build ID: //p')" >> "$GITHUB_OUTPUT"

      - name: Upload log hash dictionary
        uses: Noelware/s3-action@2.3.1
        with:
          access-key-id: ${{ secrets.LOG_HASH_BUCKET_KEY_ID }}
          secret-key: ${{ secrets.LOG_HASH_BUCKET_SECRET }}
          endpoint: ${{ vars.LOG_HASH_BUCKET_ENDPOINT }}
          bucket: ${{ vars.LOG_HASH_BUCKET_NAME }}
          files: |
            build/src/fw/tintin_fw_loghash_dict.json
          path-format: ${{ steps.build_id.outputs.BUILD_ID }}-${{ github.sha }}-normal.json

      - name: Build PRF
        run: ./waf build_prf

      - name: Bundle PRF
        run: ./waf bundle_prf

      - name: Copy PRF artifacts
        run: |
          mkdir -p artifacts
          cp build/prf/src/fw/tintin_fw.hex artifacts/prf_${{ matrix.board }}_${{github.ref_name}}.hex
          cp build/prf/src/fw/tintin_fw.bin artifacts/prf_${{ matrix.board }}_${{github.ref_name}}.bin
          cp build/prf/*.pbz artifacts
          cp build/src/fw/tintin_fw_loghash_dict.json artifacts/prf_${{ matrix.board }}_${{github.ref_name}}_loghash_dict.json

      - name: Get PRF Build ID
        id: prf_build_id
        run: |
          echo "BUILD_ID=$(arm-none-eabi-readelf -n build/prf/src/fw/tintin_fw.elf | sed -n -e 's/^.*Build ID: //p')" >> "$GITHUB_OUTPUT"

      - name: Upload PRF log hash dictionary
        uses: Noelware/s3-action@2.3.1
        with:
          access-key-id: ${{ secrets.LOG_HASH_BUCKET_KEY_ID }}
          secret-key: ${{ secrets.LOG_HASH_BUCKET_SECRET }}
          endpoint: ${{ vars.LOG_HASH_BUCKET_ENDPOINT }}
          bucket: ${{ vars.LOG_HASH_BUCKET_NAME }}
          files: |
            build/prf/src/fw/tintin_fw_loghash_dict.json
          path-format: ${{ steps.prf_build_id.outputs.BUILD_ID }}-${{ github.sha }}-prf.json

      - name: Configure PRF MFG
        run: ./waf configure --board ${{ matrix.board }} --mfg --nohash --release

      - name: Build MFG PRF
        run: ./waf build_prf

      - name: Copy MFG PRF artifacts
        run: |
          mkdir -p artifacts
          cp build/prf/src/fw/tintin_fw.hex artifacts/prf_mfg_${{ matrix.board }}_${{github.ref_name}}.hex

      - name: Store
        uses: actions/upload-artifact@v4
        with:
          name: artifacts-${{ matrix.board }}
          path: artifacts

      - name: Upload to Memfault
        run: |
          pip install memfault-cli==${MEMFAULT_CLI_VERSION}

          memfault \
            --org-token ${{ secrets.MEMFAULT_ORG_TOKEN }} \
            --org ${{ secrets.MEMFAULT_ORG }} \
            --project ${{ secrets.MEMFAULT_PROJECT }} \
            upload-mcu-symbols \
            artifacts/firmware_${{ matrix.board }}_${{github.ref_name}}.elf

          memfault \
            --org-token ${{ secrets.MEMFAULT_ORG_TOKEN }} \
            --org ${{ secrets.MEMFAULT_ORG }} \
            --project ${{ secrets.MEMFAULT_PROJECT }} \
            upload-ota-payload \
            --hardware-version ${{ matrix.board }} \
            --software-type pebbleos \
            --software-version ${{ github.ref_name }} \
            artifacts/normal_${{ matrix.board }}_${{ github.ref_name }}.pbz

          memfault \
            --org-token ${{ secrets.MEMFAULT_ORG_TOKEN }} \
            --org ${{ secrets.MEMFAULT_ORG }} \
            --project ${{ secrets.MEMFAULT_PROJECT }} \
            deploy-release \
            --release-version ${{ github.ref_name }} \
            --cohort internal
            

  release:
    runs-on: ubuntu-24.04

    needs: build
    permissions:
      contents: write

    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v4

      - name: Display artifacts
        run: ls -R

      - name: Create release
        uses: softprops/action-gh-release@v2.2.2
        with:
          files: artifacts-*/*
