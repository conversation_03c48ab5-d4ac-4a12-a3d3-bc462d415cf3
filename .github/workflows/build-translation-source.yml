name: Build Translation Source

on:
  push:
    branches: [main]
    paths-ignore:
      - 'resources/normal/base/lang/**/*.po'
      - 'resources/normal/base/lang/tintin.pot'

jobs:
  build:
    runs-on: ubuntu-24.04
    if: github.repository == 'pebble-dev/pebble-firmware'

    env:
      BOARD_NAME: "asterix"

    container:
      image: ghcr.io/pebble-dev/pebbleos-docker:v1

    steps:
      - name: Mark Github workspace as safe
        run: git config --system --add safe.directory "${GITHUB_WORKSPACE}" 

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: true

      - name: Install Python dependencies
        run: |
          pip install -U pip
          pip install -r requirements.txt

      - name: Configure
        run: ./waf configure --board ${{ env.BOARD_NAME }}

      - name: Build
        run: ./waf build

      - name: Copy POT template
        run: cp build/src/fw/tintin.pot resources/normal/base/lang/tintin.pot

      - name: Upload POT template to Crow<PERSON>
        uses: crowdin/github-action@v2
        with:
          upload_sources: true
          upload_translations: true
          config: crowdin.yml
        env:
          CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}
          CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
