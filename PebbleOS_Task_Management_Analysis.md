# PebbleOS Task Management System Analysis and Porting Guide

## Executive Summary

This document provides a comprehensive analysis of PebbleOS's unique task management system and detailed porting instructions for the Ambiq Apollo 4 Blue Plus. PebbleOS implements a sophisticated hybrid privilege model with process isolation that goes far beyond typical embedded RTOS designs.

## What Makes PebbleOS Task Management Special

### 1. Hybrid Privilege Model with Process Isolation

PebbleOS implements a **two-tier privilege system** that's unique for embedded systems:

```c
void pebble_task_create(PebbleTask pebble_task, TaskParameters_t *task_params,
                        TaskHandle_t *handle) {
  MpuRegion app_region;
  MpuRegion worker_region;
  switch (pebble_task) {
    case PebbleTask_App:
      mpu_init_region_from_region(&app_region, memory_layout_get_app_region(),
                                  true /* allow_user_access */);
      mpu_init_region_from_region(&worker_region, memory_layout_get_worker_region(),
                                  false /* allow_user_access */);
      break;
    case PebbleTask_Worker:
      mpu_init_region_from_region(&app_region, memory_layout_get_app_region(),
                                  false /* allow_user_access */);
      mpu_init_region_from_region(&worker_region, memory_layout_get_worker_region(),
                                  true /* allow_user_access */);
      break;
```

**Key Features:**
- **Per-task MPU configuration**: Each task gets its own memory protection setup
- **Dynamic privilege switching**: Apps run unprivileged but can make syscalls to gain temporary privilege
- **Cross-task isolation**: Apps can't access worker memory and vice versa

### 2. Sophisticated Task Taxonomy

```c
typedef enum PebbleTask {
  PebbleTask_KernelMain,        // Main system task
  PebbleTask_KernelBackground,  // Background system services
  PebbleTask_Worker,            // Background app worker
  PebbleTask_App,               // Foreground application
  PebbleTask_BTHost,            // Bluetooth Host
  PebbleTask_BTController,      // Bluetooth Controller
  PebbleTask_BTHCI,             // Bluetooth HCI
  PebbleTask_NewTimers,         // Timer management
  PebbleTask_PULSE,             // Debug/development
  NumPebbleTask,
  PebbleTask_Unknown
} PebbleTask;
```

**Key Features:**
- **Functional separation**: Each task type has a specific role and privilege level
- **Resource isolation**: Different heaps, stacks, and memory regions per task type
- **Event routing**: Task-aware event system routes messages appropriately

### 3. Syscall-Based Privilege Escalation

```c
#define DEFINE_SYSCALL(retType, funcName, ...) \
  retType NAKED_FUNC SECTION(".syscall_text." #funcName) funcName(__VA_ARGS__) { \
    __asm volatile (\
      "push { lr } \n" \
      "bl syscall_internal_maybe_skip_privilege \n" \
      "svc 2 \n" \
      "b __" #funcName "\n" \
    );\
  }
```

**Key Features:**
- **Automatic privilege management**: Syscalls automatically elevate privileges when needed
- **Security validation**: Syscalls validate that pointers are in userspace memory
- **Transparent operation**: Apps don't need to know about privilege levels

### 4. Task-Aware Resource Management

```c
Heap *task_heap_get_for_current_task(void) {
  if (pebble_task_get_current() == PebbleTask_App) {
    return app_state_get_heap();
  } else if (pebble_task_get_current() == PebbleTask_Worker) {
    return worker_state_get_heap();
  }
  return kernel_heap_get();
}
```

**Key Features:**
- **Automatic heap selection**: Memory allocation automatically uses the correct heap
- **Resource cleanup**: When tasks terminate, their resources are automatically freed
- **Isolation**: Tasks can't accidentally free each other's memory

## Pros and Cons Analysis

### Pros

#### 1. Excellent Security and Stability
- **Memory protection**: Apps can't crash the system or access other apps' data
- **Resource isolation**: Memory leaks in apps don't affect the system
- **Privilege separation**: System services are protected from app bugs

#### 2. Developer-Friendly
- **Transparent operation**: Developers don't need to manage privileges manually
- **Automatic resource management**: Memory allocation "just works"
- **Clean APIs**: Task-aware services provide consistent interfaces

#### 3. Robust Architecture
- **Process isolation**: Apps can be killed and restarted without affecting the system
- **Event-driven design**: Clean separation of concerns
- **Scalable**: Easy to add new task types and services

#### 4. Power Efficiency
- **Selective task suspension**: Unused tasks can be suspended to save power
- **Event-driven wake-up**: Tasks only run when they have work to do
- **Resource optimization**: Shared resources reduce memory overhead

### Cons

#### 1. Complexity
- **High implementation complexity**: Requires deep understanding of ARM MPU and FreeRTOS
- **Debugging challenges**: Multi-task, multi-privilege debugging is complex
- **Learning curve**: Developers need to understand the task model

#### 2. Resource Overhead
- **Memory overhead**: Each task type needs its own heap and stack
- **MPU configuration overhead**: Setting up memory regions takes time and space
- **Context switching cost**: Privilege changes add overhead

#### 3. Hardware Dependencies
- **MPU requirement**: Requires ARM Cortex-M with MPU (not available on M0/M0+)
- **FreeRTOS MPU dependency**: Requires FreeRTOS with MPU support
- **Platform-specific code**: Memory layout and MPU configuration is platform-specific

#### 4. Limited Flexibility
- **Fixed task types**: Adding new task types requires system-level changes
- **Static memory layout**: Memory regions are typically fixed at compile time
- **Syscall overhead**: Frequent privilege escalation can impact performance

## Apollo 4 Blue Plus Compatibility Analysis

### Excellent Hardware Fit

#### 1. Hardware Support
- **ARM Cortex-M4F**: Full MPU support with 8 configurable regions
- **192 MHz**: Sufficient performance for context switching overhead
- **1.4MB Flash + 384KB SRAM**: Adequate memory for multi-heap architecture
- **FPU**: Supports floating-point operations in apps

#### 2. MPU Capabilities
- **8 MPU regions**: Sufficient for PebbleOS's memory layout
- **Privilege levels**: Full support for privileged/unprivileged modes
- **Memory attributes**: Supports cacheable/non-cacheable, read/write permissions
- **Stack protection**: Can implement stack guard regions

#### 3. FreeRTOS Integration
- **FreeRTOS MPU**: Apollo 4 supports FreeRTOS with MPU features
- **Task creation**: `xTaskCreateRestricted()` available
- **Memory regions**: Per-task MPU configuration supported

### Potential Challenges

#### 1. Memory Layout Adaptation
- **SRAM organization**: Need to adapt to Apollo 4's memory map
- **Flash layout**: Syscall sections need proper placement
- **Stack sizing**: Balance between isolation and memory efficiency

#### 2. Power Management Integration
- **Low-power modes**: Integrate with Apollo 4's advanced power management
- **Clock gating**: Coordinate with Apollo 4's clock control
- **Wake-up sources**: Adapt to Apollo 4's interrupt structure

## Detailed Porting Implementation

### Phase 1: Basic Task Infrastructure (Week 1-2)

#### Step 1.1: Task Enumeration and Registry

**File: `pebble_tasks_apollo4.h`**
```c
#ifndef PEBBLE_TASKS_APOLLO4_H
#define PEBBLE_TASKS_APOLLO4_H

#include "FreeRTOS.h"
#include "task.h"

// Start with minimal task set for initial implementation
typedef enum PebbleTask {
  PebbleTask_KernelMain,
  PebbleTask_App,
  PebbleTask_Worker,
  NumPebbleTask,
  PebbleTask_Unknown
} PebbleTask;

// Task registry functions
void pebble_task_register(PebbleTask task, TaskHandle_t task_handle);
void pebble_task_unregister(PebbleTask task);
PebbleTask pebble_task_get_current(void);
TaskHandle_t pebble_task_get_handle_for_task(PebbleTask task);
const char* pebble_task_get_name(PebbleTask task);

// Task creation with MPU support
void pebble_task_create_apollo4(PebbleTask pebble_task,
                                TaskParameters_t *task_params,
                                TaskHandle_t *handle);

#endif // PEBBLE_TASKS_APOLLO4_H
```

**File: `pebble_tasks_apollo4.c`**
```c
#include "pebble_tasks_apollo4.h"
#include <string.h>

// Global task registry
static TaskHandle_t g_task_handles[NumPebbleTask] = { 0 };

// Task name lookup table
static const char* s_task_names[NumPebbleTask] = {
  [PebbleTask_KernelMain] = "KernelMain",
  [PebbleTask_App] = "App",
  [PebbleTask_Worker] = "Worker",
};

void pebble_task_register(PebbleTask task, TaskHandle_t task_handle) {
  if (task < NumPebbleTask) {
    g_task_handles[task] = task_handle;
  }
}

void pebble_task_unregister(PebbleTask task) {
  if (task < NumPebbleTask) {
    g_task_handles[task] = NULL;
  }
}

PebbleTask pebble_task_get_current(void) {
  TaskHandle_t current = xTaskGetCurrentTaskHandle();
  for (int i = 0; i < NumPebbleTask; i++) {
    if (g_task_handles[i] == current) {
      return (PebbleTask)i;
    }
  }
  return PebbleTask_Unknown;
}

TaskHandle_t pebble_task_get_handle_for_task(PebbleTask task) {
  if (task < NumPebbleTask) {
    return g_task_handles[task];
  }
  return NULL;
}

const char* pebble_task_get_name(PebbleTask task) {
  if (task < NumPebbleTask) {
    return s_task_names[task];
  }
  return "Unknown";
}
```

#### Step 1.2: Memory Layout for Apollo 4

**File: `memory_layout_apollo4.h`**
```c
#ifndef MEMORY_LAYOUT_APOLLO4_H
#define MEMORY_LAYOUT_APOLLO4_H

#include <stdint.h>
#include <stdbool.h>

// Apollo 4 Blue Plus memory map
#define APOLLO4_SRAM_BASE     0x10000000
#define APOLLO4_SRAM_SIZE     (384 * 1024)
#define APOLLO4_FLASH_BASE    0x00000000
#define APOLLO4_FLASH_SIZE    (1408 * 1024)  // 1.4MB

// Memory region allocation for PebbleOS
#define KERNEL_HEAP_BASE      0x10000000
#define KERNEL_HEAP_SIZE      (96 * 1024)    // 96KB

#define APP_REGION_BASE       0x10018000
#define APP_REGION_SIZE       (128 * 1024)   // 128KB

#define WORKER_REGION_BASE    0x10038000
#define WORKER_REGION_SIZE    (64 * 1024)    // 64KB

#define STACK_REGION_BASE     0x10048000
#define STACK_REGION_SIZE     (96 * 1024)    // 96KB

// MPU region structure for Apollo 4
typedef enum MpuCachePolicy {
  MpuCachePolicy_NotCacheable,
  MpuCachePolicy_WriteThrough,
  MpuCachePolicy_WriteBackWriteAllocate,
  MpuCachePolicy_WriteBackNoWriteAllocate,
} MpuCachePolicy;

typedef struct MpuRegion {
  uint8_t region_num;
  bool enabled;
  uintptr_t base_address;
  uint32_t size;
  MpuCachePolicy cache_policy;
  bool priv_read;
  bool priv_write;
  bool user_read;
  bool user_write;
} MpuRegion;

// Memory layout functions
void memory_layout_apollo4_init(void);
const MpuRegion* memory_layout_get_app_region(void);
const MpuRegion* memory_layout_get_worker_region(void);
const MpuRegion* memory_layout_get_kernel_region(void);

#endif // MEMORY_LAYOUT_APOLLO4_H
```

**File: `memory_layout_apollo4.c`**
```c
#include "memory_layout_apollo4.h"
#include "mpu_apollo4.h"

// Predefined memory regions for Apollo 4
static const MpuRegion s_kernel_region = {
  .region_num = 1,
  .enabled = true,
  .base_address = KERNEL_HEAP_BASE,
  .size = KERNEL_HEAP_SIZE,
  .cache_policy = MpuCachePolicy_WriteBackWriteAllocate,
  .priv_read = true,
  .priv_write = true,
  .user_read = false,
  .user_write = false,
};

static const MpuRegion s_app_region = {
  .region_num = 2,
  .enabled = true,
  .base_address = APP_REGION_BASE,
  .size = APP_REGION_SIZE,
  .cache_policy = MpuCachePolicy_WriteBackWriteAllocate,
  .priv_read = true,
  .priv_write = true,
  .user_read = true,
  .user_write = true,
};

static const MpuRegion s_worker_region = {
  .region_num = 3,
  .enabled = true,
  .base_address = WORKER_REGION_BASE,
  .size = WORKER_REGION_SIZE,
  .cache_policy = MpuCachePolicy_WriteBackWriteAllocate,
  .priv_read = true,
  .priv_write = true,
  .user_read = true,
  .user_write = true,
};

void memory_layout_apollo4_init(void) {
  apollo4_mpu_setup();
}

const MpuRegion* memory_layout_get_app_region(void) {
  return &s_app_region;
}

const MpuRegion* memory_layout_get_worker_region(void) {
  return &s_worker_region;
}

const MpuRegion* memory_layout_get_kernel_region(void) {
  return &s_kernel_region;
}
```

#### Step 1.3: MPU Configuration for Apollo 4

**File: `mpu_apollo4.h`**
```c
#ifndef MPU_APOLLO4_H
#define MPU_APOLLO4_H

#include "memory_layout_apollo4.h"

// MPU configuration functions
void apollo4_mpu_setup(void);
void apollo4_mpu_configure_region(const MpuRegion* region);
void apollo4_mpu_enable_region(uint8_t region_num, bool enable);
void apollo4_mpu_disable_all_regions(void);

// MPU fault handling
void apollo4_mpu_fault_handler(void);

#endif // MPU_APOLLO4_H
```

**File: `mpu_apollo4.c`**
```c
#include "mpu_apollo4.h"
#include "FreeRTOS.h"
#include "task.h"

// ARM Cortex-M4 MPU register definitions
#define MPU_TYPE_DREGION_Pos    8U
#define MPU_TYPE_DREGION_Msk    (0xFFUL << MPU_TYPE_DREGION_Pos)

// Size encoding for MPU regions
static uint32_t apollo4_mpu_encode_size(uint32_t size) {
  // Find the power of 2 that matches the size
  uint32_t encoded = 0;
  uint32_t temp_size = size;

  while (temp_size > 1) {
    temp_size >>= 1;
    encoded++;
  }

  // MPU size field is (log2(size) - 1)
  return (encoded - 1) << MPU_RASR_SIZE_Pos;
}

void apollo4_mpu_setup(void) {
  // Disable MPU during configuration
  __DMB();
  SCB->SHCSR &= ~SCB_SHCSR_MEMFAULTENA_Msk;
  MPU->CTRL = 0;

  // Check MPU support
  uint32_t mpu_type = MPU->TYPE;
  uint32_t num_regions = (mpu_type & MPU_TYPE_DREGION_Msk) >> MPU_TYPE_DREGION_Pos;
  configASSERT(num_regions >= 4); // Need at least 4 regions

  // Configure basic regions

  // Region 0: Flash (read-only, executable for all)
  MPU->RNR = 0;
  MPU->RBAR = APOLLO4_FLASH_BASE | MPU_RBAR_VALID_Msk | 0;
  MPU->RASR = apollo4_mpu_encode_size(APOLLO4_FLASH_SIZE) |
              (0x06 << MPU_RASR_AP_Pos) |    // RO for all
              MPU_RASR_C_Msk |               // Cacheable
              MPU_RASR_ENABLE_Msk;

  // Region 1: Kernel SRAM (privileged only)
  MPU->RNR = 1;
  MPU->RBAR = KERNEL_HEAP_BASE | MPU_RBAR_VALID_Msk | 1;
  MPU->RASR = apollo4_mpu_encode_size(KERNEL_HEAP_SIZE) |
              (0x01 << MPU_RASR_AP_Pos) |    // RW privileged only
              MPU_RASR_C_Msk |
              MPU_RASR_ENABLE_Msk;

  // Regions 2-3 will be configured dynamically per task

  // Enable MPU with default background region for privileged access
  MPU->CTRL = MPU_CTRL_ENABLE_Msk | MPU_CTRL_PRIVDEFENA_Msk;
  SCB->SHCSR |= SCB_SHCSR_MEMFAULTENA_Msk;
  __DSB();
  __ISB();
}

void apollo4_mpu_configure_region(const MpuRegion* region) {
  if (!region || !region->enabled) {
    return;
  }

  // Select region
  MPU->RNR = region->region_num;

  // Configure base address
  MPU->RBAR = region->base_address | MPU_RBAR_VALID_Msk | region->region_num;

  // Configure attributes
  uint32_t rasr = apollo4_mpu_encode_size(region->size);

  // Access permissions
  uint32_t ap = 0;
  if (region->priv_read && region->priv_write) {
    if (region->user_read && region->user_write) {
      ap = 0x03; // RW for all
    } else if (region->user_read) {
      ap = 0x02; // RW privileged, RO user
    } else {
      ap = 0x01; // RW privileged only
    }
  } else if (region->priv_read) {
    if (region->user_read) {
      ap = 0x06; // RO for all
    } else {
      ap = 0x05; // RO privileged only
    }
  }
  rasr |= (ap << MPU_RASR_AP_Pos);

  // Cache policy
  if (region->cache_policy == MpuCachePolicy_WriteBackWriteAllocate) {
    rasr |= MPU_RASR_C_Msk | MPU_RASR_B_Msk;
  } else if (region->cache_policy == MpuCachePolicy_WriteThrough) {
    rasr |= MPU_RASR_C_Msk;
  }

  rasr |= MPU_RASR_ENABLE_Msk;

  // Apply configuration
  MPU->RASR = rasr;
  __DSB();
  __ISB();
}

void apollo4_mpu_enable_region(uint8_t region_num, bool enable) {
  MPU->RNR = region_num;
  uint32_t rasr = MPU->RASR;

  if (enable) {
    rasr |= MPU_RASR_ENABLE_Msk;
  } else {
    rasr &= ~MPU_RASR_ENABLE_Msk;
  }

  MPU->RASR = rasr;
  __DSB();
  __ISB();
}

void apollo4_mpu_disable_all_regions(void) {
  for (int i = 2; i < 8; i++) { // Skip regions 0-1 (flash and kernel)
    apollo4_mpu_enable_region(i, false);
  }
}

void apollo4_mpu_fault_handler(void) {
  uint32_t cfsr = SCB->CFSR;
  if (cfsr & SCB_CFSR_MMARVALID_Msk) {
    uint32_t fault_addr = SCB->MMFAR;

    // Log fault information
    PebbleTask current_task = pebble_task_get_current();
    // TODO: Add logging system
    // PBL_LOG(LOG_LEVEL_ERROR, "MPU violation in task %s at 0x%08X",
    //         pebble_task_get_name(current_task), fault_addr);

    // Clear fault flags
    SCB->CFSR = cfsr;

    // Terminate the offending task if it's not a kernel task
    if (current_task == PebbleTask_App || current_task == PebbleTask_Worker) {
      vTaskDelete(NULL);
    } else {
      // Kernel task fault - this is serious, halt system
      configASSERT(0);
    }
  }
}
```

### Phase 2: Task Creation with MPU (Week 2-3)

#### Step 2.1: Apollo 4 Task Creation with MPU Support

**File: `pebble_tasks_apollo4.c` (continued)**
```c
void pebble_task_create_apollo4(PebbleTask pebble_task,
                                TaskParameters_t *task_params,
                                TaskHandle_t *handle) {
  // Configure MPU regions based on task type
  MemoryRegion_t regions[portNUM_CONFIGURABLE_REGIONS] = {0};

  switch (pebble_task) {
    case PebbleTask_App:
      // App gets access to app region only
      regions[0].ulRegionBaseAddress = APP_REGION_BASE;
      regions[0].ulRegionSize = APP_REGION_SIZE;
      regions[0].ulRegionAttribute =
        apollo4_mpu_encode_size(APP_REGION_SIZE) |
        (0x03 << MPU_RASR_AP_Pos) |   // RW for all
        MPU_RASR_C_Msk |
        MPU_RASR_ENABLE_Msk;
      break;

    case PebbleTask_Worker:
      // Worker gets access to worker region only
      regions[0].ulRegionBaseAddress = WORKER_REGION_BASE;
      regions[0].ulRegionSize = WORKER_REGION_SIZE;
      regions[0].ulRegionAttribute =
        apollo4_mpu_encode_size(WORKER_REGION_SIZE) |
        (0x03 << MPU_RASR_AP_Pos) |   // RW for all
        MPU_RASR_C_Msk |
        MPU_RASR_ENABLE_Msk;
      break;

    case PebbleTask_KernelMain:
      // Kernel tasks run privileged, no additional regions needed
      break;

    default:
      configASSERT(0); // Unknown task type
      return;
  }

  // Set up task parameters
  task_params->xRegions = regions;

  // For non-kernel tasks, run unprivileged
  if (pebble_task != PebbleTask_KernelMain) {
    task_params->uxPriority &= ~portPRIVILEGE_BIT;
  }

  // Create restricted task
  TaskHandle_t new_handle;
  BaseType_t result = xTaskCreateRestricted(task_params, &new_handle);
  configASSERT(result == pdTRUE);

  // Register task in our registry
  pebble_task_register(pebble_task, new_handle);

  if (handle) {
    *handle = new_handle;
  }
}
```

#### Step 2.2: Task State Management

**File: `task_state_apollo4.h`**
```c
#ifndef TASK_STATE_APOLLO4_H
#define TASK_STATE_APOLLO4_H

#include "pebble_tasks_apollo4.h"
#include "heap_apollo4.h"

// Task state structure
typedef struct TaskState {
  PebbleTask task_type;
  TaskHandle_t task_handle;
  Heap* task_heap;
  void* graphics_context;
  bool is_running;
  uint32_t creation_time;
} TaskState;

// Task state management
void task_state_init(void);
TaskState* task_state_get_for_task(PebbleTask task);
TaskState* task_state_get_current(void);
void task_state_cleanup(PebbleTask task);

// Task lifecycle
void task_state_mark_running(PebbleTask task, bool running);
void task_state_set_heap(PebbleTask task, Heap* heap);
void task_state_set_graphics_context(PebbleTask task, void* ctx);

#endif // TASK_STATE_APOLLO4_H
```

**File: `task_state_apollo4.c`**
```c
#include "task_state_apollo4.h"
#include <string.h>

// Task state registry
static TaskState s_task_states[NumPebbleTask] = {0};

void task_state_init(void) {
  memset(s_task_states, 0, sizeof(s_task_states));

  // Initialize each task state
  for (int i = 0; i < NumPebbleTask; i++) {
    s_task_states[i].task_type = (PebbleTask)i;
    s_task_states[i].task_handle = NULL;
    s_task_states[i].task_heap = NULL;
    s_task_states[i].graphics_context = NULL;
    s_task_states[i].is_running = false;
    s_task_states[i].creation_time = 0;
  }
}

TaskState* task_state_get_for_task(PebbleTask task) {
  if (task < NumPebbleTask) {
    return &s_task_states[task];
  }
  return NULL;
}

TaskState* task_state_get_current(void) {
  PebbleTask current_task = pebble_task_get_current();
  return task_state_get_for_task(current_task);
}

void task_state_cleanup(PebbleTask task) {
  TaskState* state = task_state_get_for_task(task);
  if (state) {
    // Clean up heap if allocated
    if (state->task_heap) {
      heap_destroy(state->task_heap);
      state->task_heap = NULL;
    }

    // Clean up graphics context
    if (state->graphics_context) {
      // TODO: Clean up graphics context
      state->graphics_context = NULL;
    }

    state->is_running = false;
    state->task_handle = NULL;
  }

  // Unregister from task registry
  pebble_task_unregister(task);
}

void task_state_mark_running(PebbleTask task, bool running) {
  TaskState* state = task_state_get_for_task(task);
  if (state) {
    state->is_running = running;
    if (running && state->creation_time == 0) {
      state->creation_time = xTaskGetTickCount();
    }
  }
}

void task_state_set_heap(PebbleTask task, Heap* heap) {
  TaskState* state = task_state_get_for_task(task);
  if (state) {
    state->task_heap = heap;
  }
}

void task_state_set_graphics_context(PebbleTask task, void* ctx) {
  TaskState* state = task_state_get_for_task(task);
  if (state) {
    state->graphics_context = ctx;
  }
}
```

### Phase 3: Syscall Implementation (Week 3-4)

#### Step 3.1: Apollo 4 Syscall Handler

**File: `syscall_apollo4.h`**
```c
#ifndef SYSCALL_APOLLO4_H
#define SYSCALL_APOLLO4_H

#include <stdint.h>
#include <stdbool.h>

// Syscall numbers
typedef enum SyscallNumber {
  SYSCALL_PRIVILEGE_ESCALATION = 2,
  SYSCALL_GET_TIME = 3,
  SYSCALL_MALLOC = 4,
  SYSCALL_FREE = 5,
  // Add more syscalls as needed
} SyscallNumber;

// Syscall infrastructure
void syscall_apollo4_init(void);
bool syscall_is_userspace_pointer(const void* ptr, size_t size);
void syscall_privilege_escalate(void);
void syscall_privilege_drop(void);

// Syscall macro for automatic privilege management
#define DEFINE_SYSCALL_APOLLO4(retType, funcName, ...) \
  retType __attribute__((naked)) funcName(__VA_ARGS__) { \
    __asm volatile ( \
      "push { lr } \n" \
      "bl syscall_privilege_escalate \n" \
      "svc %0 \n" \
      "b __" #funcName "\n" \
      :: "i" (SYSCALL_PRIVILEGE_ESCALATION) \
    ); \
  }

#endif // SYSCALL_APOLLO4_H
```

**File: `syscall_apollo4.c`**
```c
#include "syscall_apollo4.h"
#include "pebble_tasks_apollo4.h"
#include "memory_layout_apollo4.h"
#include "FreeRTOS.h"
#include "task.h"

// SVC Handler for Apollo 4
void SVC_Handler(void) {
  uint32_t *svc_args;
  uint32_t svc_number;

  // Determine which stack pointer to use
  __asm volatile (
    "tst lr, #4 \n"
    "ite eq \n"
    "mrseq %0, msp \n"
    "mrsne %0, psp \n"
    : "=r" (svc_args)
  );

  // Get SVC number from instruction
  svc_number = ((char *)svc_args[6])[-2];

  switch (svc_number) {
    case SYSCALL_PRIVILEGE_ESCALATION:
      // Switch to privileged mode
      __set_CONTROL(__get_CONTROL() & ~0x1);
      __ISB();
      break;

    case SYSCALL_GET_TIME:
      // Handle get_time syscall
      svc_args[0] = (uint32_t)xTaskGetTickCount();
      break;

    case SYSCALL_MALLOC:
      // Handle malloc syscall - validate parameters first
      {
        size_t size = svc_args[0];
        if (size > 0 && size < 64*1024) { // Reasonable size limit
          // TODO: Call task-aware malloc
          svc_args[0] = 0; // Return NULL for now
        } else {
          svc_args[0] = 0; // Invalid size
        }
      }
      break;

    case SYSCALL_FREE:
      // Handle free syscall - validate pointer first
      {
        void* ptr = (void*)svc_args[0];
        if (syscall_is_userspace_pointer(ptr, 1)) {
          // TODO: Call task-aware free
        }
      }
      break;

    default:
      // Unknown syscall - this is an error
      configASSERT(0);
      break;
  }
}

void syscall_apollo4_init(void) {
  // Configure SVC priority - must be lower than configMAX_SYSCALL_INTERRUPT_PRIORITY
  NVIC_SetPriority(SVCall_IRQn, configMAX_SYSCALL_INTERRUPT_PRIORITY);

  // Enable memory fault for MPU violations
  NVIC_SetPriority(MemoryManagement_IRQn, 0);
  NVIC_EnableIRQ(MemoryManagement_IRQn);
}

bool syscall_is_userspace_pointer(const void* ptr, size_t size) {
  uintptr_t addr = (uintptr_t)ptr;
  uintptr_t end_addr = addr + size - 1;

  PebbleTask current_task = pebble_task_get_current();

  switch (current_task) {
    case PebbleTask_App:
      return (addr >= APP_REGION_BASE &&
              end_addr < APP_REGION_BASE + APP_REGION_SIZE);

    case PebbleTask_Worker:
      return (addr >= WORKER_REGION_BASE &&
              end_addr < WORKER_REGION_BASE + WORKER_REGION_SIZE);

    case PebbleTask_KernelMain:
      // Kernel can access anything
      return true;

    default:
      return false;
  }
}

void syscall_privilege_escalate(void) {
  // Check if we're already privileged
  uint32_t control = __get_CONTROL();
  if ((control & 0x1) == 0) {
    return; // Already privileged
  }

  // Trigger SVC to escalate privilege
  __asm volatile ("svc %0" :: "i" (SYSCALL_PRIVILEGE_ESCALATION));
}

void syscall_privilege_drop(void) {
  // Only drop privilege for non-kernel tasks
  PebbleTask current_task = pebble_task_get_current();
  if (current_task != PebbleTask_KernelMain) {
    __set_CONTROL(__get_CONTROL() | 0x1);
    __ISB();
  }
}

// Memory fault handler
void MemManage_Handler(void) {
  apollo4_mpu_fault_handler();
}
```

#### Step 3.2: Example Syscall Implementation

**File: `syscall_examples_apollo4.c`**
```c
#include "syscall_apollo4.h"
#include "heap_apollo4.h"

// Example syscall: task-aware malloc
void* __sys_malloc(size_t size);

DEFINE_SYSCALL_APOLLO4(void*, sys_malloc, size_t size)

void* __sys_malloc(size_t size) {
  // This runs in privileged mode
  PebbleTask current_task = pebble_task_get_current();
  TaskState* state = task_state_get_for_task(current_task);

  if (state && state->task_heap) {
    void* ptr = heap_malloc(state->task_heap, size);
    syscall_privilege_drop(); // Drop privilege before returning
    return ptr;
  }

  syscall_privilege_drop();
  return NULL;
}

// Example syscall: task-aware free
void __sys_free(void* ptr);

DEFINE_SYSCALL_APOLLO4(void, sys_free, void* ptr)

void __sys_free(void* ptr) {
  // Validate pointer is in userspace
  if (!syscall_is_userspace_pointer(ptr, 1)) {
    syscall_privilege_drop();
    return;
  }

  PebbleTask current_task = pebble_task_get_current();
  TaskState* state = task_state_get_for_task(current_task);

  if (state && state->task_heap) {
    heap_free(state->task_heap, ptr);
  }

  syscall_privilege_drop();
}

// Example syscall: get current time
uint32_t __sys_get_time(void);

DEFINE_SYSCALL_APOLLO4(uint32_t, sys_get_time, void)

uint32_t __sys_get_time(void) {
  uint32_t time = xTaskGetTickCount();
  syscall_privilege_drop();
  return time;
}
```

### Phase 4: Integration and Configuration (Week 4-5)

#### Step 4.1: FreeRTOS Configuration for Apollo 4

**File: `FreeRTOSConfig_apollo4.h`**
```c
#ifndef FREERTOS_CONFIG_APOLLO4_H
#define FREERTOS_CONFIG_APOLLO4_H

// Apollo 4 Blue Plus specific settings
#define configCPU_CLOCK_HZ                  192000000
#define configTICK_RATE_HZ                  1000
#define configMAX_PRIORITIES                8
#define configMINIMAL_STACK_SIZE            128
#define configTOTAL_HEAP_SIZE               (200 * 1024)  // 200KB for FreeRTOS heap

// MPU specific configuration
#define configUSE_MPU_WRAPPERS              1
#define configENABLE_MPU                    1
#define configTOTAL_MPU_REGIONS             8
#define configFIRST_MPU_REGION              0
#define configLAST_MPU_REGION               7

// Task configuration
#define configUSE_PREEMPTION                1
#define configUSE_IDLE_HOOK                 1
#define configUSE_TICK_HOOK                 0
#define configUSE_TASK_NOTIFICATIONS        1
#define configNUM_THREAD_LOCAL_STORAGE_POINTERS 2

// Memory management
#define configSUPPORT_DYNAMIC_ALLOCATION    1
#define configSUPPORT_STATIC_ALLOCATION     1
#define configUSE_MALLOC_FAILED_HOOK        1

// Interrupt configuration
#define configKERNEL_INTERRUPT_PRIORITY     (7 << 5)  // Lowest priority
#define configMAX_SYSCALL_INTERRUPT_PRIORITY (5 << 5) // Higher than kernel

// Debug configuration
#define configUSE_TRACE_FACILITY            1
#define configUSE_STATS_FORMATTING_FUNCTIONS 1
#define configGENERATE_RUN_TIME_STATS       1

// Stack overflow detection
#define configCHECK_FOR_STACK_OVERFLOW      2

// Task stack sizes for different task types
#define configKERNEL_MAIN_STACK_SIZE        (2 * 1024)   // 2KB
#define configAPP_STACK_SIZE                (1.5 * 1024) // 1.5KB
#define configWORKER_STACK_SIZE             (1 * 1024)   // 1KB

#endif // FREERTOS_CONFIG_APOLLO4_H
```

#### Step 4.2: System Initialization

**File: `system_init_apollo4.h`**
```c
#ifndef SYSTEM_INIT_APOLLO4_H
#define SYSTEM_INIT_APOLLO4_H

// System initialization functions
void system_init_apollo4(void);
void system_create_kernel_tasks(void);
void system_start_scheduler(void);

// Task entry points
void kernel_main_task(void* params);
void test_app_task(void* params);
void test_worker_task(void* params);

#endif // SYSTEM_INIT_APOLLO4_H
```

**File: `system_init_apollo4.c`**
```c
#include "system_init_apollo4.h"
#include "pebble_tasks_apollo4.h"
#include "memory_layout_apollo4.h"
#include "syscall_apollo4.h"
#include "heap_apollo4.h"
#include "FreeRTOS.h"
#include "task.h"

void system_init_apollo4(void) {
  // Initialize hardware
  // TODO: Add Apollo 4 specific hardware initialization

  // Initialize memory layout and MPU
  memory_layout_apollo4_init();

  // Initialize syscall system
  syscall_apollo4_init();

  // Initialize task state management
  task_state_init();

  // Initialize heap system
  heap_system_init();

  // Create kernel tasks
  system_create_kernel_tasks();
}

void system_create_kernel_tasks(void) {
  // Create kernel main task
  TaskParameters_t kernel_params = {
    .pvTaskCode = kernel_main_task,
    .pcName = "KernelMain",
    .usStackDepth = configKERNEL_MAIN_STACK_SIZE / sizeof(StackType_t),
    .uxPriority = configMAX_PRIORITIES - 1 | portPRIVILEGE_BIT,
    .puxStackBuffer = NULL, // Use dynamic allocation
    .xRegions = { 0 }, // Kernel has access to everything
  };

  TaskHandle_t kernel_handle;
  pebble_task_create_apollo4(PebbleTask_KernelMain, &kernel_params, &kernel_handle);
}

void system_start_scheduler(void) {
  // Start FreeRTOS scheduler
  vTaskStartScheduler();

  // Should never reach here
  configASSERT(0);
}

void kernel_main_task(void* params) {
  (void)params;

  // Mark kernel as running
  task_state_mark_running(PebbleTask_KernelMain, true);

  // Initialize kernel services
  // TODO: Initialize event system, timer services, etc.

  // Create test tasks for validation
  TaskParameters_t app_params = {
    .pvTaskCode = test_app_task,
    .pcName = "TestApp",
    .usStackDepth = configAPP_STACK_SIZE / sizeof(StackType_t),
    .uxPriority = 2, // Unprivileged
    .puxStackBuffer = NULL,
    .xRegions = { 0 }, // Will be configured by pebble_task_create_apollo4
  };

  TaskHandle_t app_handle;
  pebble_task_create_apollo4(PebbleTask_App, &app_params, &app_handle);

  // Main kernel loop
  while (1) {
    // Process kernel events
    // TODO: Add event processing

    vTaskDelay(pdMS_TO_TICKS(10));
  }
}

void test_app_task(void* params) {
  (void)params;

  // Mark app as running
  task_state_mark_running(PebbleTask_App, true);

  // Test syscalls
  uint32_t time = sys_get_time();
  void* ptr = sys_malloc(100);

  if (ptr) {
    // Use the memory
    memset(ptr, 0xAA, 100);
    sys_free(ptr);
  }

  // App main loop
  while (1) {
    // App logic here
    vTaskDelay(pdMS_TO_TICKS(100));
  }
}

void test_worker_task(void* params) {
  (void)params;

  // Mark worker as running
  task_state_mark_running(PebbleTask_Worker, true);

  // Worker main loop
  while (1) {
    // Worker logic here
    vTaskDelay(pdMS_TO_TICKS(1000));
  }
}
```

## Comprehensive Testing Strategy

### Unit Tests

#### Test 1: Task Registry Functionality

**File: `test_task_registry.c`**
```c
#include "unity.h"
#include "pebble_tasks_apollo4.h"

void setUp(void) {
  // Reset task registry before each test
  for (int i = 0; i < NumPebbleTask; i++) {
    pebble_task_unregister((PebbleTask)i);
  }
}

void tearDown(void) {
  // Clean up after each test
}

void test_task_registration(void) {
  TaskHandle_t dummy_handle = (TaskHandle_t)0x12345678;

  // Test registration
  pebble_task_register(PebbleTask_App, dummy_handle);
  TEST_ASSERT_EQUAL_PTR(dummy_handle, pebble_task_get_handle_for_task(PebbleTask_App));

  // Test unregistration
  pebble_task_unregister(PebbleTask_App);
  TEST_ASSERT_NULL(pebble_task_get_handle_for_task(PebbleTask_App));
}

void test_task_name_lookup(void) {
  TEST_ASSERT_EQUAL_STRING("KernelMain", pebble_task_get_name(PebbleTask_KernelMain));
  TEST_ASSERT_EQUAL_STRING("App", pebble_task_get_name(PebbleTask_App));
  TEST_ASSERT_EQUAL_STRING("Worker", pebble_task_get_name(PebbleTask_Worker));
  TEST_ASSERT_EQUAL_STRING("Unknown", pebble_task_get_name(PebbleTask_Unknown));
}

void test_current_task_detection(void) {
  // This test requires running in a FreeRTOS context
  // Mock xTaskGetCurrentTaskHandle() for unit testing
  TaskHandle_t mock_handle = (TaskHandle_t)0xABCDEF00;

  pebble_task_register(PebbleTask_App, mock_handle);

  // Mock the current task handle
  // TEST_ASSERT_EQUAL(PebbleTask_App, pebble_task_get_current());
}

int main(void) {
  UNITY_BEGIN();

  RUN_TEST(test_task_registration);
  RUN_TEST(test_task_name_lookup);
  RUN_TEST(test_current_task_detection);

  return UNITY_END();
}
```

#### Test 2: Memory Layout Validation

**File: `test_memory_layout.c`**
```c
#include "unity.h"
#include "memory_layout_apollo4.h"

void setUp(void) {
  memory_layout_apollo4_init();
}

void tearDown(void) {
}

void test_memory_regions_non_overlapping(void) {
  const MpuRegion* app_region = memory_layout_get_app_region();
  const MpuRegion* worker_region = memory_layout_get_worker_region();
  const MpuRegion* kernel_region = memory_layout_get_kernel_region();

  // Test that regions don't overlap
  uintptr_t app_end = app_region->base_address + app_region->size;
  uintptr_t worker_start = worker_region->base_address;
  uintptr_t kernel_end = kernel_region->base_address + kernel_region->size;
  uintptr_t app_start = app_region->base_address;

  TEST_ASSERT_LESS_OR_EQUAL(worker_start, app_end);
  TEST_ASSERT_LESS_OR_EQUAL(app_start, kernel_end);
}

void test_memory_regions_within_sram(void) {
  const MpuRegion* regions[] = {
    memory_layout_get_app_region(),
    memory_layout_get_worker_region(),
    memory_layout_get_kernel_region()
  };

  for (int i = 0; i < 3; i++) {
    const MpuRegion* region = regions[i];

    // Check base address is within SRAM
    TEST_ASSERT_GREATER_OR_EQUAL(APOLLO4_SRAM_BASE, region->base_address);

    // Check end address is within SRAM
    uintptr_t end_addr = region->base_address + region->size;
    TEST_ASSERT_LESS_OR_EQUAL(APOLLO4_SRAM_BASE + APOLLO4_SRAM_SIZE, end_addr);
  }
}

void test_region_sizes_reasonable(void) {
  const MpuRegion* app_region = memory_layout_get_app_region();
  const MpuRegion* worker_region = memory_layout_get_worker_region();
  const MpuRegion* kernel_region = memory_layout_get_kernel_region();

  // Test minimum sizes
  TEST_ASSERT_GREATER_OR_EQUAL(64 * 1024, app_region->size);    // At least 64KB
  TEST_ASSERT_GREATER_OR_EQUAL(32 * 1024, worker_region->size); // At least 32KB
  TEST_ASSERT_GREATER_OR_EQUAL(32 * 1024, kernel_region->size); // At least 32KB

  // Test maximum sizes (shouldn't exceed total SRAM)
  TEST_ASSERT_LESS_OR_EQUAL(APOLLO4_SRAM_SIZE, app_region->size);
  TEST_ASSERT_LESS_OR_EQUAL(APOLLO4_SRAM_SIZE, worker_region->size);
  TEST_ASSERT_LESS_OR_EQUAL(APOLLO4_SRAM_SIZE, kernel_region->size);
}

int main(void) {
  UNITY_BEGIN();

  RUN_TEST(test_memory_regions_non_overlapping);
  RUN_TEST(test_memory_regions_within_sram);
  RUN_TEST(test_region_sizes_reasonable);

  return UNITY_END();
}
```

#### Test 3: MPU Configuration

**File: `test_mpu_config.c`**
```c
#include "unity.h"
#include "mpu_apollo4.h"
#include "memory_layout_apollo4.h"

void setUp(void) {
  apollo4_mpu_setup();
}

void tearDown(void) {
  apollo4_mpu_disable_all_regions();
}

void test_mpu_basic_setup(void) {
  // Test that MPU is enabled
  TEST_ASSERT_TRUE(MPU->CTRL & MPU_CTRL_ENABLE_Msk);

  // Test that background region is enabled for privileged access
  TEST_ASSERT_TRUE(MPU->CTRL & MPU_CTRL_PRIVDEFENA_Msk);

  // Test that memory fault is enabled
  TEST_ASSERT_TRUE(SCB->SHCSR & SCB_SHCSR_MEMFAULTENA_Msk);
}

void test_region_configuration(void) {
  const MpuRegion* app_region = memory_layout_get_app_region();

  // Configure app region
  apollo4_mpu_configure_region(app_region);

  // Read back configuration
  MPU->RNR = app_region->region_num;
  uint32_t rbar = MPU->RBAR;
  uint32_t rasr = MPU->RASR;

  // Test base address
  TEST_ASSERT_EQUAL_HEX32(app_region->base_address, rbar & MPU_RBAR_ADDR_Msk);

  // Test that region is enabled
  TEST_ASSERT_TRUE(rasr & MPU_RASR_ENABLE_Msk);
}

void test_region_enable_disable(void) {
  // Enable region 2
  apollo4_mpu_enable_region(2, true);
  MPU->RNR = 2;
  TEST_ASSERT_TRUE(MPU->RASR & MPU_RASR_ENABLE_Msk);

  // Disable region 2
  apollo4_mpu_enable_region(2, false);
  MPU->RNR = 2;
  TEST_ASSERT_FALSE(MPU->RASR & MPU_RASR_ENABLE_Msk);
}

int main(void) {
  UNITY_BEGIN();

  RUN_TEST(test_mpu_basic_setup);
  RUN_TEST(test_region_configuration);
  RUN_TEST(test_region_enable_disable);

  return UNITY_END();
}
```

### Integration Tests

#### Test 4: Task Creation and Isolation

**File: `test_task_isolation.c`**
```c
#include "unity.h"
#include "pebble_tasks_apollo4.h"
#include "system_init_apollo4.h"
#include "FreeRTOS.h"
#include "task.h"

// Test task functions
static volatile bool s_app_task_started = false;
static volatile bool s_worker_task_started = false;
static volatile bool s_memory_fault_occurred = false;

void test_app_task_func(void* params) {
  s_app_task_started = true;

  // Test that we're running unprivileged
  uint32_t control = __get_CONTROL();
  TEST_ASSERT_TRUE(control & 0x1); // Should be unprivileged

  // Test memory allocation in app region
  void* ptr = sys_malloc(100);
  TEST_ASSERT_NOT_NULL(ptr);

  // Write to allocated memory (should work)
  memset(ptr, 0xAA, 100);

  sys_free(ptr);

  // Test syscall functionality
  uint32_t time = sys_get_time();
  TEST_ASSERT_GREATER_THAN(0, time);

  vTaskDelete(NULL);
}

void test_worker_task_func(void* params) {
  s_worker_task_started = true;

  // Test that we're running unprivileged
  uint32_t control = __get_CONTROL();
  TEST_ASSERT_TRUE(control & 0x1); // Should be unprivileged

  vTaskDelete(NULL);
}

void memory_fault_test_task(void* params) {
  // Try to access kernel memory (should fault)
  volatile uint32_t* kernel_ptr = (uint32_t*)KERNEL_HEAP_BASE;

  // This should trigger a memory fault
  *kernel_ptr = 0xDEADBEEF;

  // Should never reach here
  TEST_FAIL_MESSAGE("Memory fault did not occur");
}

void setUp(void) {
  s_app_task_started = false;
  s_worker_task_started = false;
  s_memory_fault_occurred = false;
}

void tearDown(void) {
  // Clean up any remaining tasks
}

void test_task_creation_and_execution(void) {
  // Create app task
  TaskParameters_t app_params = {
    .pvTaskCode = test_app_task_func,
    .pcName = "TestApp",
    .usStackDepth = 512,
    .uxPriority = 2,
    .puxStackBuffer = NULL,
    .xRegions = { 0 },
  };

  TaskHandle_t app_handle;
  pebble_task_create_apollo4(PebbleTask_App, &app_params, &app_handle);

  // Create worker task
  TaskParameters_t worker_params = {
    .pvTaskCode = test_worker_task_func,
    .pcName = "TestWorker",
    .usStackDepth = 512,
    .uxPriority = 1,
    .puxStackBuffer = NULL,
    .xRegions = { 0 },
  };

  TaskHandle_t worker_handle;
  pebble_task_create_apollo4(PebbleTask_Worker, &worker_params, &worker_handle);

  // Wait for tasks to start and complete
  vTaskDelay(pdMS_TO_TICKS(100));

  TEST_ASSERT_TRUE(s_app_task_started);
  TEST_ASSERT_TRUE(s_worker_task_started);
}

void test_memory_protection(void) {
  // Create a task that will try to violate memory protection
  TaskParameters_t fault_params = {
    .pvTaskCode = memory_fault_test_task,
    .pcName = "FaultTest",
    .usStackDepth = 512,
    .uxPriority = 2,
    .puxStackBuffer = NULL,
    .xRegions = { 0 },
  };

  TaskHandle_t fault_handle;
  pebble_task_create_apollo4(PebbleTask_App, &fault_params, &fault_handle);

  // Wait for the task to fault and be terminated
  vTaskDelay(pdMS_TO_TICKS(100));

  // Task should have been terminated by the fault handler
  eTaskState task_state = eTaskGetState(fault_handle);
  TEST_ASSERT_EQUAL(eDeleted, task_state);
}

void test_task_state_management(void) {
  TaskState* app_state = task_state_get_for_task(PebbleTask_App);
  TEST_ASSERT_NOT_NULL(app_state);
  TEST_ASSERT_EQUAL(PebbleTask_App, app_state->task_type);

  // Test state transitions
  task_state_mark_running(PebbleTask_App, true);
  TEST_ASSERT_TRUE(app_state->is_running);

  task_state_mark_running(PebbleTask_App, false);
  TEST_ASSERT_FALSE(app_state->is_running);
}

int main(void) {
  // Initialize system for integration tests
  system_init_apollo4();

  UNITY_BEGIN();

  RUN_TEST(test_task_creation_and_execution);
  RUN_TEST(test_memory_protection);
  RUN_TEST(test_task_state_management);

  return UNITY_END();
}
```

#### Test 5: Syscall Functionality

**File: `test_syscalls.c`**
```c
#include "unity.h"
#include "syscall_apollo4.h"
#include "pebble_tasks_apollo4.h"

void test_syscall_task(void* params) {
  // Test privilege escalation
  uint32_t control_before = __get_CONTROL();
  TEST_ASSERT_TRUE(control_before & 0x1); // Should start unprivileged

  // Make a syscall
  uint32_t time = sys_get_time();
  TEST_ASSERT_GREATER_THAN(0, time);

  // Should be back to unprivileged after syscall
  uint32_t control_after = __get_CONTROL();
  TEST_ASSERT_TRUE(control_after & 0x1);

  vTaskDelete(NULL);
}

void setUp(void) {
  syscall_apollo4_init();
}

void tearDown(void) {
}

void test_privilege_escalation(void) {
  // This test needs to run in unprivileged mode
  // Create an unprivileged task to test syscalls
  TaskParameters_t task_params = {
    .pvTaskCode = test_syscall_task,
    .pcName = "SyscallTest",
    .usStackDepth = 512,
    .uxPriority = 2,
    .puxStackBuffer = NULL,
    .xRegions = { 0 },
  };

  TaskHandle_t task_handle;
  pebble_task_create_apollo4(PebbleTask_App, &task_params, &task_handle);

  // Wait for task to complete
  vTaskDelay(pdMS_TO_TICKS(100));
}

void test_userspace_pointer_validation(void) {
  // Test valid app pointers
  void* app_ptr = (void*)APP_REGION_BASE;
  TEST_ASSERT_TRUE(syscall_is_userspace_pointer(app_ptr, 100));

  // Test invalid kernel pointers
  void* kernel_ptr = (void*)KERNEL_HEAP_BASE;
  TEST_ASSERT_FALSE(syscall_is_userspace_pointer(kernel_ptr, 100));

  // Test boundary conditions
  void* boundary_ptr = (void*)(APP_REGION_BASE + APP_REGION_SIZE - 1);
  TEST_ASSERT_TRUE(syscall_is_userspace_pointer(boundary_ptr, 1));

  void* overflow_ptr = (void*)(APP_REGION_BASE + APP_REGION_SIZE);
  TEST_ASSERT_FALSE(syscall_is_userspace_pointer(overflow_ptr, 1));
}

void test_malloc_free_syscalls(void) {
  // Test allocation
  void* ptr = sys_malloc(100);
  TEST_ASSERT_NOT_NULL(ptr);

  // Test that pointer is in valid range
  TEST_ASSERT_TRUE(syscall_is_userspace_pointer(ptr, 100));

  // Test usage
  memset(ptr, 0x55, 100);

  // Test free
  sys_free(ptr);

  // Test invalid free (should not crash)
  sys_free(NULL);
  sys_free((void*)KERNEL_HEAP_BASE);
}

int main(void) {
  UNITY_BEGIN();

  RUN_TEST(test_privilege_escalation);
  RUN_TEST(test_userspace_pointer_validation);
  RUN_TEST(test_malloc_free_syscalls);

  return UNITY_END();
}
```

### System-Level Tests

#### Test 6: Performance and Stress Testing

**File: `test_performance.c`**
```c
#include "unity.h"
#include "pebble_tasks_apollo4.h"
#include "FreeRTOS.h"
#include "task.h"

#define STRESS_TEST_ITERATIONS 1000
#define STRESS_TEST_TASKS 5

static volatile uint32_t s_context_switch_count = 0;
static volatile uint32_t s_syscall_count = 0;

void stress_test_task(void* params) {
  int task_id = (int)params;

  for (int i = 0; i < STRESS_TEST_ITERATIONS; i++) {
    // Test memory allocation/deallocation
    void* ptr = sys_malloc(64 + (task_id * 16));
    if (ptr) {
      memset(ptr, task_id, 64 + (task_id * 16));
      sys_free(ptr);
    }

    // Test syscalls
    uint32_t time = sys_get_time();
    s_syscall_count++;

    // Force context switch
    vTaskDelay(1);
    s_context_switch_count++;
  }

  vTaskDelete(NULL);
}

void setUp(void) {
  s_context_switch_count = 0;
  s_syscall_count = 0;
}

void tearDown(void) {
}

void test_context_switch_performance(void) {
  uint32_t start_time = xTaskGetTickCount();

  // Create multiple tasks that will context switch frequently
  TaskHandle_t task_handles[STRESS_TEST_TASKS];

  for (int i = 0; i < STRESS_TEST_TASKS; i++) {
    TaskParameters_t task_params = {
      .pvTaskCode = stress_test_task,
      .pcName = "StressTask",
      .usStackDepth = 512,
      .uxPriority = 2 + i,
      .puxStackBuffer = NULL,
      .xRegions = { 0 },
    };

    pebble_task_create_apollo4(PebbleTask_App, &task_params, &task_handles[i]);
  }

  // Wait for all tasks to complete
  bool all_completed = false;
  while (!all_completed) {
    all_completed = true;
    for (int i = 0; i < STRESS_TEST_TASKS; i++) {
      if (eTaskGetState(task_handles[i]) != eDeleted) {
        all_completed = false;
        break;
      }
    }
    vTaskDelay(pdMS_TO_TICKS(10));
  }

  uint32_t end_time = xTaskGetTickCount();
  uint32_t total_time = end_time - start_time;

  // Performance assertions
  TEST_ASSERT_GREATER_THAN(0, s_context_switch_count);
  TEST_ASSERT_GREATER_THAN(0, s_syscall_count);

  // Should complete within reasonable time (adjust based on requirements)
  TEST_ASSERT_LESS_THAN(pdMS_TO_TICKS(10000), total_time); // 10 seconds max

  printf("Context switches: %lu, Syscalls: %lu, Time: %lu ms\n",
         s_context_switch_count, s_syscall_count, total_time);
}

void test_memory_fragmentation(void) {
  #define ALLOC_COUNT 100
  void* ptrs[ALLOC_COUNT];

  // Allocate many small blocks
  for (int i = 0; i < ALLOC_COUNT; i++) {
    ptrs[i] = sys_malloc(32 + (i % 64));
    TEST_ASSERT_NOT_NULL(ptrs[i]);
  }

  // Free every other block to create fragmentation
  for (int i = 0; i < ALLOC_COUNT; i += 2) {
    sys_free(ptrs[i]);
    ptrs[i] = NULL;
  }

  // Try to allocate larger blocks
  void* large_ptr = sys_malloc(1024);
  TEST_ASSERT_NOT_NULL(large_ptr);

  // Clean up
  sys_free(large_ptr);
  for (int i = 1; i < ALLOC_COUNT; i += 2) {
    if (ptrs[i]) {
      sys_free(ptrs[i]);
    }
  }
}

void test_stack_usage(void) {
  // Test that tasks don't overflow their stacks
  TaskHandle_t current = xTaskGetCurrentTaskHandle();
  UBaseType_t stack_high_water = uxTaskGetStackHighWaterMark(current);

  // Should have reasonable stack usage (not too close to overflow)
  TEST_ASSERT_GREATER_THAN(100, stack_high_water); // At least 100 words free

  printf("Stack high water mark: %lu words\n", stack_high_water);
}

int main(void) {
  UNITY_BEGIN();

  RUN_TEST(test_context_switch_performance);
  RUN_TEST(test_memory_fragmentation);
  RUN_TEST(test_stack_usage);

  return UNITY_END();
}
```

#### Test 7: Error Handling and Recovery

**File: `test_error_handling.c`**
```c
#include "unity.h"
#include "pebble_tasks_apollo4.h"
#include "mpu_apollo4.h"

static volatile bool s_fault_handler_called = false;
static volatile uint32_t s_fault_address = 0;

// Override fault handler for testing
void test_mpu_fault_handler(void) {
  s_fault_handler_called = true;

  uint32_t cfsr = SCB->CFSR;
  if (cfsr & SCB_CFSR_MMARVALID_Msk) {
    s_fault_address = SCB->MMFAR;
  }

  // Clear fault flags
  SCB->CFSR = cfsr;

  // Terminate current task
  vTaskDelete(NULL);
}

void fault_test_task(void* params) {
  uint32_t fault_type = (uint32_t)params;

  switch (fault_type) {
    case 1: // Access kernel memory
      {
        volatile uint32_t* kernel_ptr = (uint32_t*)KERNEL_HEAP_BASE;
        *kernel_ptr = 0xDEADBEEF;
      }
      break;

    case 2: // Access worker memory from app
      {
        volatile uint32_t* worker_ptr = (uint32_t*)WORKER_REGION_BASE;
        *worker_ptr = 0xDEADBEEF;
      }
      break;

    case 3: // Stack overflow simulation
      {
        volatile char big_array[2048]; // Larger than typical stack
        memset((void*)big_array, 0xAA, sizeof(big_array));
      }
      break;
  }

  // Should not reach here if fault occurred
  TEST_FAIL_MESSAGE("Expected fault did not occur");
}

void setUp(void) {
  s_fault_handler_called = false;
  s_fault_address = 0;
}

void tearDown(void) {
}

void test_kernel_memory_protection(void) {
  TaskParameters_t task_params = {
    .pvTaskCode = fault_test_task,
    .pcName = "FaultTest1",
    .usStackDepth = 512,
    .uxPriority = 2,
    .puxStackBuffer = NULL,
    .xRegions = { 0 },
  };

  TaskHandle_t task_handle;
  pebble_task_create_apollo4(PebbleTask_App, &task_params, &task_handle);

  // Wait for fault to occur
  vTaskDelay(pdMS_TO_TICKS(100));

  // Task should be terminated
  eTaskState state = eTaskGetState(task_handle);
  TEST_ASSERT_EQUAL(eDeleted, state);
}

void test_cross_task_memory_protection(void) {
  TaskParameters_t task_params = {
    .pvTaskCode = fault_test_task,
    .pcName = "FaultTest2",
    .usStackDepth = 512,
    .uxPriority = 2,
    .puxStackBuffer = NULL,
    .xRegions = { 0 },
  };

  TaskHandle_t task_handle;
  pebble_task_create_apollo4(PebbleTask_App, &task_params, &task_handle);

  // Wait for fault to occur
  vTaskDelay(pdMS_TO_TICKS(100));

  // Task should be terminated
  eTaskState state = eTaskGetState(task_handle);
  TEST_ASSERT_EQUAL(eDeleted, state);
}

void test_invalid_syscall_parameters(void) {
  // Test malloc with invalid size
  void* ptr1 = sys_malloc(0);
  TEST_ASSERT_NULL(ptr1);

  void* ptr2 = sys_malloc(SIZE_MAX);
  TEST_ASSERT_NULL(ptr2);

  // Test free with invalid pointer
  sys_free(NULL); // Should not crash
  sys_free((void*)0xDEADBEEF); // Should not crash
}

void test_resource_cleanup_on_task_death(void) {
  // Create a task that allocates memory then dies
  TaskParameters_t task_params = {
    .pvTaskCode = fault_test_task,
    .pcName = "CleanupTest",
    .usStackDepth = 512,
    .uxPriority = 2,
    .puxStackBuffer = NULL,
    .xRegions = { 0 },
  };

  TaskHandle_t task_handle;
  pebble_task_create_apollo4(PebbleTask_App, &task_params, &task_handle);

  // Wait for task to die
  vTaskDelay(pdMS_TO_TICKS(100));

  // Check that task state was cleaned up
  TaskState* state = task_state_get_for_task(PebbleTask_App);
  TEST_ASSERT_FALSE(state->is_running);
  TEST_ASSERT_NULL(state->task_handle);
}

int main(void) {
  UNITY_BEGIN();

  RUN_TEST(test_kernel_memory_protection);
  RUN_TEST(test_cross_task_memory_protection);
  RUN_TEST(test_invalid_syscall_parameters);
  RUN_TEST(test_resource_cleanup_on_task_death);

  return UNITY_END();
}
```

## Test Execution Strategy

### Automated Test Suite

**File: `run_all_tests.sh`**
```bash
#!/bin/bash

echo "Running PebbleOS Task Management Test Suite"
echo "==========================================="

# Unit tests
echo "Running Unit Tests..."
./test_task_registry
./test_memory_layout
./test_mpu_config

# Integration tests
echo "Running Integration Tests..."
./test_task_isolation
./test_syscalls

# System tests
echo "Running System Tests..."
./test_performance
./test_error_handling

echo "All tests completed!"
```

### Continuous Integration

**File: `test_config.cmake`**
```cmake
# Test configuration for Apollo 4 Blue Plus
set(APOLLO4_TEST_CONFIG ON)
set(UNITY_FRAMEWORK_PATH "${CMAKE_SOURCE_DIR}/third_party/unity")

# Include Unity framework
include_directories(${UNITY_FRAMEWORK_PATH}/src)

# Test targets
add_executable(test_task_registry test_task_registry.c ${UNITY_FRAMEWORK_PATH}/src/unity.c)
add_executable(test_memory_layout test_memory_layout.c ${UNITY_FRAMEWORK_PATH}/src/unity.c)
add_executable(test_mpu_config test_mpu_config.c ${UNITY_FRAMEWORK_PATH}/src/unity.c)
add_executable(test_task_isolation test_task_isolation.c ${UNITY_FRAMEWORK_PATH}/src/unity.c)
add_executable(test_syscalls test_syscalls.c ${UNITY_FRAMEWORK_PATH}/src/unity.c)
add_executable(test_performance test_performance.c ${UNITY_FRAMEWORK_PATH}/src/unity.c)
add_executable(test_error_handling test_error_handling.c ${UNITY_FRAMEWORK_PATH}/src/unity.c)

# Link with task management library
target_link_libraries(test_task_registry pebble_tasks_apollo4)
target_link_libraries(test_memory_layout pebble_tasks_apollo4)
target_link_libraries(test_mpu_config pebble_tasks_apollo4)
target_link_libraries(test_task_isolation pebble_tasks_apollo4)
target_link_libraries(test_syscalls pebble_tasks_apollo4)
target_link_libraries(test_performance pebble_tasks_apollo4)
target_link_libraries(test_error_handling pebble_tasks_apollo4)
```

## Key Implementation Considerations

### Memory Layout Optimization for Apollo 4
- **SRAM efficiency**: 384KB requires careful allocation between kernel, app, and worker heaps
- **Cache alignment**: Align memory regions to cache line boundaries for optimal performance
- **Stack sizing**: Balance between isolation and memory efficiency

### Performance Optimization
- **Context switch overhead**: Minimize MPU reconfiguration time during task switches
- **Syscall efficiency**: Optimize privilege escalation paths for frequently used syscalls
- **Memory access patterns**: Design heap layout to work well with Apollo 4's cache

### Power Management Integration
- **Task suspension**: Coordinate with Apollo 4's low-power modes when tasks are idle
- **Clock gating**: Disable clocks for unused peripherals when tasks are suspended
- **Wake-up optimization**: Use Apollo 4's advanced interrupt capabilities for efficient task wake-up

This comprehensive testing strategy ensures that the ported task management system is robust, secure, and performs well on the Apollo 4 Blue Plus platform. The tests cover everything from basic functionality to stress testing and error recovery, providing confidence in the implementation's reliability.

## Conclusion

PebbleOS's task management system represents a sophisticated approach to embedded system security and resource management. Its hybrid privilege model, combined with MPU-based process isolation, provides excellent security while maintaining developer-friendly APIs.

The Apollo 4 Blue Plus is an excellent platform for this system, with sufficient MPU regions, memory, and performance to support the full feature set. The porting approach outlined here provides a solid foundation for building the rest of the PebbleOS UI system on top of this secure, isolated architecture.

The comprehensive testing strategy ensures that the implementation is robust and reliable, covering unit tests, integration tests, and system-level validation. This approach will help identify issues early and ensure the ported system meets the high standards of the original PebbleOS.
```