#!/usr/bin/env python

from __future__ import print_function
import os
import platform
import sys
import subprocess

arguments = sys.argv[1:]

LINUX_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'pdc2png_linux')
OSX_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'pdc2png_osx')

if platform.system() == 'Darwin':
  cmd = [OSX_PATH]
elif platform.system() == 'Linux':
  cmd = [LINUX_PATH]
else:
  raise Exception("Your operating system is not supported")

cmd.extend(arguments)
p = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
out, err = p.communicate()
print(out, file=sys.stdout)
print(err, file=sys.stderr)

sys.exit(p.returncode)
