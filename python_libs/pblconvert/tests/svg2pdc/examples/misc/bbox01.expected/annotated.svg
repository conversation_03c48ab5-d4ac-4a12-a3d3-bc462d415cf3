<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:pdc="http://www.pebble.com/2015/pdc" version="1.1" width="380px" height="120px" viewBox="0 0 380 120">
  <title>Bounding Box of a Path</title>
  <desc>
    Illustration of one shape (a 'path' element with a quadratic B&#233;zier) with
    three bounding boxes, only one of which is correct.
  </desc>
  <defs>
    <g id="shape">
      <line x1="120" y1="50" x2="70" y2="10" stroke="#888"/>
      <line x1="20" y1="50" x2="70" y2="10" stroke="#888"/>
      <path stroke-width="2" fill="rgb(173, 216, 230)" stroke="none" fill-rule="evenodd" d="M20,50                L35,100                H120                V50                Q70,10 20,50"/>
      <circle cx="120" cy="50" r="3" fill="none" stroke="#888"/>
      <circle cx="20" cy="50" r="3" fill="none" stroke="#888"/>
      <circle cx="70" cy="10" r="3" fill="#888" stroke="none"/>
    </g>
  </defs>
  <g text-anchor="middle">
    <g>
      <title>Correct Bounding Box</title>
      <use xlink:href="#shape"/>
      <rect x="20" y="30" width="100" height="70" fill="none" stroke="green" stroke-dasharray="2" stroke-linecap="round"/>
      <!--<text x='70' y='115'>Correct</text>-->
    </g>
    <g transform="translate(120)">
      <title>Incorrect Bounding Box</title>
      <desc>Bounding box does not encompass entire shape.</desc>
      <use xlink:href="#shape"/>
      <rect x="20" y="50" width="100" height="50" fill="none" stroke="red" stroke-dasharray="2" stroke-linecap="round"/>
      <!--<text x='70' y='115'>Incorrect</text>-->
    </g>
    <g transform="translate(240)">
      <title>Incorrect Bounding Box</title>
      <desc>Bounding box includes control points outside shape.</desc>
      <use xlink:href="#shape"/>
      <rect x="20" y="10" width="100" height="90" fill="none" stroke="red" stroke-dasharray="2" stroke-linecap="round"/>
      <!--<text x='70' y='115'>Incorrect</text>-->
    </g>
  </g>
</svg>

