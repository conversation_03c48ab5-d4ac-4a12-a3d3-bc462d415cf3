<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:pdc="http://www.pebble.com/2015/pdc" version="1.1" id="Design" x="0px" y="0px" viewBox="0 0 82 102" enable-background="new 0 0 82 102" xml:space="preserve">
  <rect x="2" y="26" fill="#FFFFFF" stroke="#000000" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" width="42" height="74"/>
  <line fill="#FFFFFF" stroke="#000000" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="2.5" y1="89.5" x2="43.5" y2="89.5"/>
  <line fill="#FFFFFF" stroke="#000000" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="43.5" y1="32.5" x2="2.5" y2="32.5"/>
  <line fill="#FFFFFF" stroke="#000000" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="25.5" y1="94.5" x2="20.5" y2="94.5"/>
  <line fill="none" stroke="#000000" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="16.5" y1="1.5" x2="22.5" y2="7.5"/>
  <line fill="none" stroke="#000000" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="16.5" y1="17.5" x2="8.5" y2="15.5"/>
  <line fill="none" stroke="#000000" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="69.5" y1="1.5" x2="63.5" y2="7.5"/>
  <line fill="none" stroke="#000000" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="69.5" y1="17.5" x2="77.5" y2="15.5"/>
  <rect x="58" y="30" fill="#FFFFFF" stroke="#000000" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" width="14" height="70"/>
  <path fill="#FFFFFF" stroke="#000000" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="  M65.3,79h-0.6C57.1,79,51,72.9,51,65.3v-0.6C51,57.1,57.1,51,64.7,51h0.6C72.9,51,79,57.1,79,64.7v0.6C79,72.9,72.9,79,65.3,79z">
    <pdc:annotation description="Element contains unsupported curved command(s)." href="https://pebbletechnology.atlassian.net/wiki/display/DEV/Pebble+Draw+Commands#PebbleDrawCommands-issue-bezier">
      <pdc:highlight y="65.30" x="51.00" height="13.70" width="13.70"/>
      <pdc:highlight y="51.00" x="51.00" height="13.70" width="13.70"/>
      <pdc:highlight y="51.00" x="65.30" height="13.70" width="13.70"/>
      <pdc:highlight y="65.30" x="65.30" height="13.70" width="13.70"/>
    </pdc:annotation>
    <pdc:annotation description="Element is expressed with unsupported coordinate(s)." href="https://pebbletechnology.atlassian.net/wiki/display/DEV/Pebble+Draw+Commands#PebbleDrawCommands-issue-pixelgrid">
      <pdc:highlight y="79.00" x="65.30" details="Invalid point: (65.30, 79.00). Used closest supported coordinate: (65.25, 79.0)"/>
      <pdc:highlight y="79.00" x="64.70" details="Invalid point: (64.70, 79.00). Used closest supported coordinate: (64.75, 79.0)"/>
      <pdc:highlight y="77.93" x="59.35" details="Invalid point: (59.35, 77.93). Used closest supported coordinate: (59.375, 77.875)"/>
      <pdc:highlight y="75.00" x="55.00" details="Invalid point: (55.00, 75.00). Used closest supported coordinate: (55.0, 75.0)"/>
      <pdc:highlight y="70.65" x="52.07" details="Invalid point: (52.07, 70.65). Used closest supported coordinate: (52.125, 70.625)"/>
      <pdc:highlight y="65.30" x="51.00" details="Invalid point: (51.00, 65.30). Used closest supported coordinate: (51.0, 65.25)"/>
      <pdc:highlight y="64.70" x="51.00" details="Invalid point: (51.00, 64.70). Used closest supported coordinate: (51.0, 64.75)"/>
      <pdc:highlight y="59.35" x="52.07" details="Invalid point: (52.07, 59.35). Used closest supported coordinate: (52.125, 59.375)"/>
      <pdc:highlight y="55.00" x="55.00" details="Invalid point: (55.00, 55.00). Used closest supported coordinate: (55.0, 55.0)"/>
      <pdc:highlight y="52.07" x="59.35" details="Invalid point: (59.35, 52.07). Used closest supported coordinate: (59.375, 52.125)"/>
      <pdc:highlight y="51.00" x="64.70" details="Invalid point: (64.70, 51.00). Used closest supported coordinate: (64.75, 51.0)"/>
      <pdc:highlight y="51.00" x="65.30" details="Invalid point: (65.30, 51.00). Used closest supported coordinate: (65.25, 51.0)"/>
      <pdc:highlight y="52.07" x="70.65" details="Invalid point: (70.65, 52.07). Used closest supported coordinate: (70.625, 52.125)"/>
      <pdc:highlight y="55.00" x="75.00" details="Invalid point: (75.00, 55.00). Used closest supported coordinate: (75.0, 55.0)"/>
      <pdc:highlight y="59.35" x="77.93" details="Invalid point: (77.93, 59.35). Used closest supported coordinate: (77.875, 59.375)"/>
      <pdc:highlight y="64.70" x="79.00" details="Invalid point: (79.00, 64.70). Used closest supported coordinate: (79.0, 64.75)"/>
      <pdc:highlight y="65.30" x="79.00" details="Invalid point: (79.00, 65.30). Used closest supported coordinate: (79.0, 65.25)"/>
      <pdc:highlight y="70.65" x="77.93" details="Invalid point: (77.93, 70.65). Used closest supported coordinate: (77.875, 70.625)"/>
      <pdc:highlight y="75.00" x="75.00" details="Invalid point: (75.00, 75.00). Used closest supported coordinate: (75.0, 75.0)"/>
      <pdc:highlight y="77.93" x="70.65" details="Invalid point: (70.65, 77.93). Used closest supported coordinate: (70.625, 77.875)"/>
      <pdc:highlight y="79.00" x="65.30" details="Invalid point: (65.30, 79.00). Used closest supported coordinate: (65.25, 79.0)"/>
    </pdc:annotation>
  </path>
  <line fill="none" stroke="#000000" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="58.5" y1="42.5" x2="72.5" y2="42.5"/>
  <path fill="none" stroke="#000000" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="  M65.3,72.5h-0.6c-4,0-7.2-3.2-7.2-7.2v-0.6c0-4,3.2-7.2,7.2-7.2h0.6c4,0,7.2,3.2,7.2,7.2v0.6C72.5,69.3,69.3,72.5,65.3,72.5z">
    <pdc:annotation description="Element contains unsupported curved command(s)." href="https://pebbletechnology.atlassian.net/wiki/display/DEV/Pebble+Draw+Commands#PebbleDrawCommands-issue-bezier">
      <pdc:highlight y="65.30" x="57.50" height="7.20" width="7.20"/>
      <pdc:highlight y="57.50" x="57.50" height="7.20" width="7.20"/>
      <pdc:highlight y="57.50" x="65.30" height="7.20" width="7.20"/>
      <pdc:highlight y="65.30" x="65.30" height="7.20" width="7.20"/>
    </pdc:annotation>
    <pdc:annotation description="Element is expressed with unsupported coordinate(s)." href="https://pebbletechnology.atlassian.net/wiki/display/DEV/Pebble+Draw+Commands#PebbleDrawCommands-issue-pixelgrid">
      <pdc:highlight y="72.50" x="65.30" details="Invalid point: (65.30, 72.50). Used closest supported coordinate: (65.25, 72.5)"/>
      <pdc:highlight y="72.50" x="64.70" details="Invalid point: (64.70, 72.50). Used closest supported coordinate: (64.75, 72.5)"/>
      <pdc:highlight y="70.40" x="59.60" details="Invalid point: (59.60, 70.40). Used closest supported coordinate: (59.625, 70.375)"/>
      <pdc:highlight y="65.30" x="57.50" details="Invalid point: (57.50, 65.30). Used closest supported coordinate: (57.5, 65.25)"/>
      <pdc:highlight y="64.70" x="57.50" details="Invalid point: (57.50, 64.70). Used closest supported coordinate: (57.5, 64.75)"/>
      <pdc:highlight y="59.60" x="59.60" details="Invalid point: (59.60, 59.60). Used closest supported coordinate: (59.625, 59.625)"/>
      <pdc:highlight y="57.50" x="64.70" details="Invalid point: (64.70, 57.50). Used closest supported coordinate: (64.75, 57.5)"/>
      <pdc:highlight y="57.50" x="65.30" details="Invalid point: (65.30, 57.50). Used closest supported coordinate: (65.25, 57.5)"/>
      <pdc:highlight y="59.60" x="70.40" details="Invalid point: (70.40, 59.60). Used closest supported coordinate: (70.375, 59.625)"/>
      <pdc:highlight y="64.70" x="72.50" details="Invalid point: (72.50, 64.70). Used closest supported coordinate: (72.5, 64.75)"/>
      <pdc:highlight y="65.30" x="72.50" details="Invalid point: (72.50, 65.30). Used closest supported coordinate: (72.5, 65.25)"/>
      <pdc:highlight y="70.40" x="70.40" details="Invalid point: (70.40, 70.40). Used closest supported coordinate: (70.375, 70.375)"/>
      <pdc:highlight y="72.50" x="65.30" details="Invalid point: (65.30, 72.50). Used closest supported coordinate: (65.25, 72.5)"/>
    </pdc:annotation>
  </path>
  <g>
    <polygon fill="#FF0000" stroke="#000000" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" points="   34,53 22,41 22,22 34,10 53,10 65,22 65,41 53,53  "/>
    <line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="35" y1="23" x2="52" y2="40"/>
    <line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="35" y1="40" x2="52" y2="23"/>
  </g>
</svg>

