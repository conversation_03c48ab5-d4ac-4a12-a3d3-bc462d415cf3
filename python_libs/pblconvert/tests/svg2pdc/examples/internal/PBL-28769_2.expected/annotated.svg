<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:pdc="http://www.pebble.com/2015/pdc" version="1.1" id="Design" x="0px" y="0px" viewBox="0 0 99 78" enable-background="new 0 0 99 78" xml:space="preserve">
  <g>
    <rect x="2" y="2" fill="#FFFFFF" stroke="#000000" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" width="42" height="74"/>
    <line fill="#FFFFFF" stroke="#000000" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="2.5" y1="65.5" x2="43.5" y2="65.5"/>
    <line fill="#FFFFFF" stroke="#000000" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="43.5" y1="8.5" x2="2.5" y2="8.5"/>
    <line fill="#FFFFFF" stroke="#000000" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="25.5" y1="70.5" x2="20.5" y2="70.5"/>
  </g>
  <rect x="75" y="2" fill="#FFFFFF" stroke="#000000" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" width="14" height="74"/>
  <path fill="#FFFFFF" stroke="#000000" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="  M82.3,55h-0.6C74.1,55,68,48.9,68,41.3v-0.6C68,33.1,74.1,27,81.7,27h0.6C89.9,27,96,33.1,96,40.7v0.6C96,48.9,89.9,55,82.3,55z">
    <pdc:annotation description="Element contains unsupported curved command(s)." href="https://pebbletechnology.atlassian.net/wiki/display/DEV/Pebble+Draw+Commands#PebbleDrawCommands-issue-bezier">
      <pdc:highlight y="41.30" x="68.00" height="13.70" width="13.70"/>
      <pdc:highlight y="27.00" x="68.00" height="13.70" width="13.70"/>
      <pdc:highlight y="27.00" x="82.30" height="13.70" width="13.70"/>
      <pdc:highlight y="41.30" x="82.30" height="13.70" width="13.70"/>
    </pdc:annotation>
    <pdc:annotation description="Element is expressed with unsupported coordinate(s)." href="https://pebbletechnology.atlassian.net/wiki/display/DEV/Pebble+Draw+Commands#PebbleDrawCommands-issue-pixelgrid">
      <pdc:highlight y="55.00" x="82.30" details="Invalid point: (82.30, 55.00). Used closest supported coordinate: (82.25, 55.0)"/>
      <pdc:highlight y="55.00" x="81.70" details="Invalid point: (81.70, 55.00). Used closest supported coordinate: (81.75, 55.0)"/>
      <pdc:highlight y="53.93" x="76.35" details="Invalid point: (76.35, 53.93). Used closest supported coordinate: (76.375, 53.875)"/>
      <pdc:highlight y="51.00" x="72.00" details="Invalid point: (72.00, 51.00). Used closest supported coordinate: (72.0, 51.0)"/>
      <pdc:highlight y="46.65" x="69.07" details="Invalid point: (69.07, 46.65). Used closest supported coordinate: (69.125, 46.625)"/>
      <pdc:highlight y="41.30" x="68.00" details="Invalid point: (68.00, 41.30). Used closest supported coordinate: (68.0, 41.25)"/>
      <pdc:highlight y="40.70" x="68.00" details="Invalid point: (68.00, 40.70). Used closest supported coordinate: (68.0, 40.75)"/>
      <pdc:highlight y="35.35" x="69.07" details="Invalid point: (69.07, 35.35). Used closest supported coordinate: (69.125, 35.375)"/>
      <pdc:highlight y="31.00" x="72.00" details="Invalid point: (72.00, 31.00). Used closest supported coordinate: (72.0, 31.0)"/>
      <pdc:highlight y="28.07" x="76.35" details="Invalid point: (76.35, 28.07). Used closest supported coordinate: (76.375, 28.125)"/>
      <pdc:highlight y="27.00" x="81.70" details="Invalid point: (81.70, 27.00). Used closest supported coordinate: (81.75, 27.0)"/>
      <pdc:highlight y="27.00" x="82.30" details="Invalid point: (82.30, 27.00). Used closest supported coordinate: (82.25, 27.0)"/>
      <pdc:highlight y="28.07" x="87.65" details="Invalid point: (87.65, 28.07). Used closest supported coordinate: (87.625, 28.125)"/>
      <pdc:highlight y="31.00" x="92.00" details="Invalid point: (92.00, 31.00). Used closest supported coordinate: (92.0, 31.0)"/>
      <pdc:highlight y="35.35" x="94.93" details="Invalid point: (94.93, 35.35). Used closest supported coordinate: (94.875, 35.375)"/>
      <pdc:highlight y="40.70" x="96.00" details="Invalid point: (96.00, 40.70). Used closest supported coordinate: (96.0, 40.75)"/>
      <pdc:highlight y="41.30" x="96.00" details="Invalid point: (96.00, 41.30). Used closest supported coordinate: (96.0, 41.25)"/>
      <pdc:highlight y="46.65" x="94.93" details="Invalid point: (94.93, 46.65). Used closest supported coordinate: (94.875, 46.625)"/>
      <pdc:highlight y="51.00" x="92.00" details="Invalid point: (92.00, 51.00). Used closest supported coordinate: (92.0, 51.0)"/>
      <pdc:highlight y="53.93" x="87.65" details="Invalid point: (87.65, 53.93). Used closest supported coordinate: (87.625, 53.875)"/>
      <pdc:highlight y="55.00" x="82.30" details="Invalid point: (82.30, 55.00). Used closest supported coordinate: (82.25, 55.0)"/>
    </pdc:annotation>
  </path>
  <line fill="none" stroke="#000000" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="75.5" y1="14.5" x2="89.5" y2="14.5"/>
  <line fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="82" y1="14.5" x2="82" y2="9.5"/>
  <path fill="none" stroke="#000000" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="  M82.3,48.5h-0.6c-4,0-7.2-3.2-7.2-7.2v-0.6c0-4,3.2-7.2,7.2-7.2h0.6c4,0,7.2,3.2,7.2,7.2v0.6C89.5,45.3,86.3,48.5,82.3,48.5z">
    <pdc:annotation description="Element contains unsupported curved command(s)." href="https://pebbletechnology.atlassian.net/wiki/display/DEV/Pebble+Draw+Commands#PebbleDrawCommands-issue-bezier">
      <pdc:highlight y="41.30" x="74.50" height="7.20" width="7.20"/>
      <pdc:highlight y="33.50" x="74.50" height="7.20" width="7.20"/>
      <pdc:highlight y="33.50" x="82.30" height="7.20" width="7.20"/>
      <pdc:highlight y="41.30" x="82.30" height="7.20" width="7.20"/>
    </pdc:annotation>
    <pdc:annotation description="Element is expressed with unsupported coordinate(s)." href="https://pebbletechnology.atlassian.net/wiki/display/DEV/Pebble+Draw+Commands#PebbleDrawCommands-issue-pixelgrid">
      <pdc:highlight y="48.50" x="82.30" details="Invalid point: (82.30, 48.50). Used closest supported coordinate: (82.25, 48.5)"/>
      <pdc:highlight y="48.50" x="81.70" details="Invalid point: (81.70, 48.50). Used closest supported coordinate: (81.75, 48.5)"/>
      <pdc:highlight y="46.40" x="76.60" details="Invalid point: (76.60, 46.40). Used closest supported coordinate: (76.625, 46.375)"/>
      <pdc:highlight y="41.30" x="74.50" details="Invalid point: (74.50, 41.30). Used closest supported coordinate: (74.5, 41.25)"/>
      <pdc:highlight y="40.70" x="74.50" details="Invalid point: (74.50, 40.70). Used closest supported coordinate: (74.5, 40.75)"/>
      <pdc:highlight y="35.60" x="76.60" details="Invalid point: (76.60, 35.60). Used closest supported coordinate: (76.625, 35.625)"/>
      <pdc:highlight y="33.50" x="81.70" details="Invalid point: (81.70, 33.50). Used closest supported coordinate: (81.75, 33.5)"/>
      <pdc:highlight y="33.50" x="82.30" details="Invalid point: (82.30, 33.50). Used closest supported coordinate: (82.25, 33.5)"/>
      <pdc:highlight y="35.60" x="87.40" details="Invalid point: (87.40, 35.60). Used closest supported coordinate: (87.375, 35.625)"/>
      <pdc:highlight y="40.70" x="89.50" details="Invalid point: (89.50, 40.70). Used closest supported coordinate: (89.5, 40.75)"/>
      <pdc:highlight y="41.30" x="89.50" details="Invalid point: (89.50, 41.30). Used closest supported coordinate: (89.5, 41.25)"/>
      <pdc:highlight y="46.40" x="87.40" details="Invalid point: (87.40, 46.40). Used closest supported coordinate: (87.375, 46.375)"/>
      <pdc:highlight y="48.50" x="82.30" details="Invalid point: (82.30, 48.50). Used closest supported coordinate: (82.25, 48.5)"/>
    </pdc:annotation>
  </path>
  <polyline fill="none" stroke="#000000" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" points="  55,49 61,43 55,37 "/>
  <line fill="none" stroke="#000000" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="61" y1="43" x2="25" y2="43"/>
</svg>

