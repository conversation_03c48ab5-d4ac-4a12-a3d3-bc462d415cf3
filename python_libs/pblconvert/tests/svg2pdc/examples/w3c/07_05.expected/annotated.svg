<svg xmlns="http://www.w3.org/2000/svg" xmlns:pdc="http://www.pebble.com/2015/pdc" version="1.2" baseProfile="tiny" width="400px" height="120px" viewBox="0 0 400 120">
  <desc>Example RotateScale - Rotate and scale transforms</desc>
  <g fill="none" stroke="black" stroke-width="3">
    <!-- Draw the axes of the original coordinate system -->
    <line x1="0" y1="1.5" x2="400" y2="1.5"/>
    <line x1="1.5" y1="0" x2="1.5" y2="120"/>
  </g>
  <!-- Establish a new coordinate system whose origin is at (50,30)
       in the initial coord. system and which is rotated by 30 degrees. -->
  <g transform="translate(50,30)">
    <g transform="rotate(30)">
      <g fill="none" stroke="red" stroke-width="3">
        <line x1="0" y1="0" x2="50" y2="0">
          <pdc:annotation description="Element is expressed with unsupported coordinate(s)." href="https://pebbletechnology.atlassian.net/wiki/display/DEV/Pebble+Draw+Commands#PebbleDrawCommands-issue-pixelgrid">
            <pdc:highlight y="55.00" x="93.30" details="Invalid point: (93.30, 55.00). Used closest supported coordinate: (93.25, 55.0)"/>
          </pdc:annotation>
        </line>
        <line x1="0" y1="0" x2="0" y2="50">
          <pdc:annotation description="Element is expressed with unsupported coordinate(s)." href="https://pebbletechnology.atlassian.net/wiki/display/DEV/Pebble+Draw+Commands#PebbleDrawCommands-issue-pixelgrid">
            <pdc:highlight y="73.30" x="25.00" details="Invalid point: (25.00, 73.30). Used closest supported coordinate: (25.0, 73.25)"/>
          </pdc:annotation>
        </line>
      </g>
      <!--<text x="0" y="0" font-size="20" font-family="Verdana" fill="blue">-->
      <!--ABC (rotate)-->
      <!--</text>-->
    </g>
  </g>
  <!-- Establish a new coordinate system whose origin is at (200,40)
       in the initial coord. system and which is scaled by 1.5. -->
  <g transform="translate(200,40)">
    <g transform="scale(1.5)">
      <g fill="none" stroke="red" stroke-width="3">
        <line x1="0" y1="0" x2="50" y2="0"/>
        <line x1="0" y1="0" x2="0" y2="50"/>
      </g>
      <!--<text x="0" y="0" font-size="20" font-family="Verdana" fill="blue">-->
      <!--ABC (scale)-->
      <!--</text>-->
    </g>
  </g>
</svg>

