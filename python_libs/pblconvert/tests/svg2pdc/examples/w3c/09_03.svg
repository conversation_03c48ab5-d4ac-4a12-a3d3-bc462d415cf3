<?xml version="1.0"?>
<svg width="12cm" height="4cm" viewBox="0 0 1200 400"
     xmlns="http://www.w3.org/2000/svg" version="1.2" baseProfile="tiny">
  <desc>Example circle01 - circle filled with red and stroked with blue</desc>
  <!-- Show outline of canvas using 'rect' element -->
  <rect x="1" y="1" width="1198" height="398"
        fill="none" stroke="blue" stroke-width="2"/>
  <circle cx="600" cy="200" r="100"
        fill="red" stroke="blue" stroke-width="10"  />

  <circle cx="900" cy="200" r="100" transform="scale(0.3, 1)"
        fill="red" stroke="blue" stroke-width="10"  />

</svg>