<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:pdc="http://www.pebble.com/2015/pdc" width="12cm" height="3.5cm" viewBox="0 0 1200 350" version="1.2" baseProfile="tiny">
  <desc>Example linecap - demonstrates three stroke-linecap values</desc>
  <rect x="1" y="1" width="1198" height="348" fill="none" stroke="blue"/>
  <defs>
    <path id="path1" d="M -125,150 L 0,0 L 125,150" fill="none"/>
    <circle fill="#ffcccc" stroke="none" id="circle1" cx="0" cy="0" r="8"/>
  </defs>
  <g transform="translate(200,75)">
    <use stroke="black" stroke-width="70" xlink:href="#path1" stroke-linejoin="miter"/>
    <use stroke="#ffcccc" stroke-width="5" xlink:href="#path1"/>
    <use xlink:href="#circle1"/>
    <text text-anchor="middle" font-size="50" font-family="Verdana" y="230">'miter' join</text>
  </g>
  <g transform="translate(600,75)">
    <use stroke="black" stroke-width="70" xlink:href="#path1" stroke-linejoin="round"/>
    <use stroke="#ffcccc" stroke-width="5" xlink:href="#path1"/>
    <use xlink:href="#circle1"/>
    <text text-anchor="middle" font-size="50" font-family="Verdana" y="230">'round' join</text>
  </g>
  <g transform="translate(1000,75)">
    <use stroke="black" stroke-width="70" xlink:href="#path1" stroke-linejoin="bevel"/>
    <use stroke="#ffcccc" stroke-width="5" xlink:href="#path1"/>
    <use xlink:href="#circle1"/>
    <text text-anchor="middle" font-size="50" font-family="Verdana" y="230">'bevel' join</text>
  </g>
</svg>

