﻿<?xml version="1.0"?>
<svg width="400px" height="150px" version="1.2" baseProfile="tiny"
     xmlns="http://www.w3.org/2000/svg">
  <desc>Example Nested - Nested transformations</desc>
  <g fill="none" stroke="black" stroke-width="3" >
    <!-- Draw the axes of the original coordinate system -->
    <line x1="0" y1="1.5" x2="400" y2="1.5" />
    <line x1="1.5" y1="0" x2="1.5" y2="150" />
  </g>
  <!-- First, a translate -->
  <g transform="translate(50,90)">
    <g fill="none" stroke="red" stroke-width="3" >
      <line x1="0" y1="0" x2="50" y2="0" />
      <line x1="0" y1="0" x2="0" y2="50" />
    </g>
    <!--<text x="0" y="0" font-size="16" font-family="Verdana" >-->
      <!--....Translate(1)-->
    <!--</text>-->
    <!-- Second, a rotate -->
    <g transform="rotate(-45)">
      <g fill="none" stroke="green" stroke-width="3" >
        <line x1="0" y1="0" x2="50" y2="0" />
        <line x1="0" y1="0" x2="0" y2="50" />
      </g>
      <!--<text x="0" y="0" font-size="16" font-family="Verdana" >-->
        <!--....Rotate(2)-->
      <!--</text>-->
      <!-- Third, another translate -->
      <g transform="translate(130,160)">
        <g fill="none" stroke="blue" stroke-width="3" >
          <line x1="0" y1="0" x2="50" y2="0" />
          <line x1="0" y1="0" x2="0" y2="50" />
        </g>
        <!--<text x="0" y="0" font-size="16" font-family="Verdana" >-->
          <!--....Translate(3)-->
        <!--</text>-->
      </g>
    </g>
  </g>
</svg>