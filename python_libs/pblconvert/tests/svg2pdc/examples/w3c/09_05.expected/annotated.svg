<svg xmlns="http://www.w3.org/2000/svg" xmlns:pdc="http://www.pebble.com/2015/pdc" width="12cm" height="4cm" viewBox="0 0 1200 400" version="1.2" baseProfile="tiny">
  <desc>Example line01 - lines expressed in user coordinates</desc>
  <!-- Show outline of canvas using 'rect' element -->
  <rect x="1" y="1" width="1198" height="398" fill="none" stroke="blue" stroke-width="2"/>
  <g stroke="green">
    <line x1="100" y1="300" x2="300" y2="100" stroke-width="5"/>
    <line x1="300" y1="300" x2="500" y2="100" stroke-width="10"/>
    <line x1="500" y1="300" x2="700" y2="100" stroke-width="15"/>
    <line x1="700" y1="300" x2="900" y2="100" stroke-width="20"/>
    <line x1="900" y1="300" x2="1100" y2="100" stroke-width="25"/>
  </g>
</svg>

