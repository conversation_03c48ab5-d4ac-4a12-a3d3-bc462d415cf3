<?xml version="1.0"?>
<svg viewBox="0 0 1200 400"  version="1.2" baseProfile="tiny"
     xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <desc>Example fillrule-evenodd - demonstrates fill-rule:evenodd</desc>

  <rect x="1" y="1" width="1198" height="398"
        fill="none" stroke="blue" />
  <defs>
    <path id="Triangle" d="M 16,0 L -8,9 v-18 z" fill="black" stroke="none" />
  </defs>
  <g fill-rule="evenodd" fill="red" stroke="black" stroke-width="3" >
    <path d="M 250,75 L 323,301 131,161 369,161 177,301 z" />
    <use xlink:href="#Triangle" transform="translate(306.21 249) rotate(72)"/>
    <use xlink:href="#Triangle" transform="translate(175.16,193.2) rotate(216)"/>
    <use xlink:href="#Triangle" transform="translate(314.26,161) rotate(0)"/>
    <use xlink:href="#Triangle" transform="translate(221.16,268.8) rotate(144)"/>
    <use xlink:href="#Triangle" transform="translate(233.21,126.98) rotate(288)"/>
	  <path d="
	   M 600, 81 c 59.196, 0, 107.18, 48.148, 107.18, 107.542 c 0, 59.387 -47.983, 107.535 -107.18, 107.535 c -59.192, 0 -107.178 -48.148 -107.178 -107.535 C 493.322, 129.048, 541.308, 81, 601, 81 z
		 M600,139 c 27.109, 0, 49.081, 22.048, 49.081, 49.249 c 0,27.193 -21.972, 49.242 -49.081, 49.242 c -27.106, 0 -49.082 -22.049 -49.082 -49.242 C 551.418, 161.242, 573.394, 139, 600, 139 z"/>
    <use xlink:href="#Triangle" transform="translate(600,188) rotate(0) translate(107,0) rotate(90)"/>
    <use xlink:href="#Triangle" transform="translate(600,188) rotate(120) translate(107,0) rotate(90)"/>
    <use xlink:href="#Triangle" transform="translate(600,188) rotate(240) translate(107,0) rotate(90)"/>
    <use xlink:href="#Triangle" transform="translate(600,188) rotate(60) translate(49,0) rotate(90)"/>
    <use xlink:href="#Triangle" transform="translate(600,188) rotate(180) translate(49,0) rotate(90)"/>
    <use xlink:href="#Triangle" transform="translate(600,188) rotate(300) translate(49,0) rotate(90)"/>
	  <path d="
	   M 950,81 c 59.192, 0, 107.177, 48.148, 107.177, 107.542 c 0, 59.387 -47.984, 107.535 -107.177, 107.535 c -59.196, 0 -107.18 -48.148 -107.18 -107.535 C 843.906, 129.048, 891.89, 81, 950, 81 z
		 M 950, 139 c -27.109, 0 -49.085, 22.048 -49.085, 49.249 c 0, 27.193, 21.976, 49.242, 49.085, 49.242 c 27.106, 0, 49.082 -22.049, 49.082 -49.242 C 1000.168, 161.242, 978.192, 139, 950, 139 z"/>
    <use xlink:href="#Triangle" transform="translate(950,188) rotate(0) translate(107,0) rotate(90)"/>
    <use xlink:href="#Triangle" transform="translate(950,188) rotate(120) translate(107,0) rotate(90)"/>
    <use xlink:href="#Triangle" transform="translate(950,188) rotate(240) translate(107,0) rotate(90)"/>
    <use xlink:href="#Triangle" transform="translate(950,188) rotate(60) translate(49,0) rotate(-90)"/>
    <use xlink:href="#Triangle" transform="translate(950,188) rotate(180) translate(49,0) rotate(-90)"/>
    <use xlink:href="#Triangle" transform="translate(950,188) rotate(300) translate(49,0) rotate(-90)"/>
  </g>
</svg>

