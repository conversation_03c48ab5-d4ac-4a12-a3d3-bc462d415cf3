def configure(cfg):
    variant = ''
    if cfg.options.nowatchdog:
        variant = 'nowatchdog_'

    if cfg.options.board in ('snowy_bb2', 'snowy_s3', 'spalding_bb2'):
        bootloader_board = 'snowy_dvt'
    elif cfg.is_silk():
        bootloader_board = 'silk'
    elif cfg.is_cutts():
        bootloader_board = 'robert_bb'
    else:
        bootloader_board = cfg.options.board

    boot_file_pattern = '%sboot_%s@*.hex' % (variant, bootloader_board)
    boot_node = cfg.path.ant_glob(boot_file_pattern)

    if not boot_node:
        cfg.end_msg('Not found')
    elif len(boot_node) > 1:
        cfg.fatal('Multiple bootloader binaries matching "%s": %s' % (
            boot_file_pattern, ', '.join(str(n) for n in boot_node)))
    else:
        cfg.env.BOOTLOADER_HEX = boot_node[0].abspath()
        cfg.end_msg(cfg.env.BOOTLOADER_HEX)

# vim:filetype=python
